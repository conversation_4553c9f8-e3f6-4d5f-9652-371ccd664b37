"""
ModernBERT-based hallucination detection model.

This module implements a token-level classification model using ModernBERT
for hallucination detection in RAG systems.
"""

import torch
import torch.nn as nn
from transformers import (
    AutoModel, 
    AutoTokenizer, 
    AutoConfig,
    PreTrainedModel,
    PretrainedConfig
)
from typing import Optional, Tuple, Dict, Any
import logging

logger = logging.getLogger(__name__)

class ModernBERTHallucinationDetectorConfig(PretrainedConfig):
    """
    Configuration class for ModernBERT Hallucination Detector.
    """
    
    model_type = "modernbert_hallucination_detector"
    
    def __init__(
        self,
        base_model_name: str = "answerdotai/ModernBERT-base",
        num_labels: int = 2,
        dropout_rate: float = 0.1,
        classifier_hidden_size: Optional[int] = None,
        use_crf: bool = False,
        **kwargs
    ):
        super().__init__(**kwargs)
        self.base_model_name = base_model_name
        self.num_labels = num_labels
        self.dropout_rate = dropout_rate
        self.classifier_hidden_size = classifier_hidden_size
        self.use_crf = use_crf

class ModernBERTHallucinationDetector(PreTrainedModel):
    """
    ModernBERT-based model for token-level hallucination detection.
    """
    
    config_class = ModernBERTHallucinationDetectorConfig
    
    def __init__(self, config: ModernBERTHallucinationDetectorConfig):
        super().__init__(config)
        
        self.config = config
        self.num_labels = config.num_labels
        
        # Load the base ModernBERT model
        self.bert = AutoModel.from_pretrained(
            config.base_model_name,
            add_pooling_layer=False
        )
        
        # Get hidden size from the base model
        self.hidden_size = self.bert.config.hidden_size
        
        # Dropout layer
        self.dropout = nn.Dropout(config.dropout_rate)
        
        # Classification head
        if config.classifier_hidden_size:
            self.classifier = nn.Sequential(
                nn.Linear(self.hidden_size, config.classifier_hidden_size),
                nn.ReLU(),
                nn.Dropout(config.dropout_rate),
                nn.Linear(config.classifier_hidden_size, config.num_labels)
            )
        else:
            self.classifier = nn.Linear(self.hidden_size, config.num_labels)
        
        # Optional CRF layer for sequence labeling
        if config.use_crf:
            try:
                from torchcrf import CRF
                self.crf = CRF(config.num_labels, batch_first=True)
            except ImportError:
                logger.warning("torchcrf not installed. CRF layer disabled.")
                self.crf = None
                config.use_crf = False
        else:
            self.crf = None
        
        # Initialize weights
        self.init_weights()
    
    def forward(
        self,
        input_ids: Optional[torch.Tensor] = None,
        attention_mask: Optional[torch.Tensor] = None,
        token_type_ids: Optional[torch.Tensor] = None,
        labels: Optional[torch.Tensor] = None,
        return_dict: Optional[bool] = None,
        **kwargs
    ) -> Dict[str, torch.Tensor]:
        """
        Forward pass of the model.
        
        Args:
            input_ids: Input token IDs
            attention_mask: Attention mask
            token_type_ids: Token type IDs (optional)
            labels: Ground truth labels for training
            return_dict: Whether to return a dictionary
            
        Returns:
            Dictionary containing loss, logits, and other outputs
        """
        return_dict = return_dict if return_dict is not None else self.config.use_return_dict
        
        # Get BERT outputs
        outputs = self.bert(
            input_ids=input_ids,
            attention_mask=attention_mask,
            token_type_ids=token_type_ids,
            return_dict=True
        )
        
        # Get sequence output (last hidden states)
        sequence_output = outputs.last_hidden_state
        
        # Apply dropout
        sequence_output = self.dropout(sequence_output)
        
        # Get logits from classifier
        logits = self.classifier(sequence_output)
        
        loss = None
        if labels is not None:
            if self.crf is not None:
                # Use CRF for loss calculation
                # Create mask for valid tokens (not padding)
                mask = labels != -100
                # Replace -100 with 0 for CRF (will be masked out)
                crf_labels = labels.clone()
                crf_labels[crf_labels == -100] = 0
                
                # Calculate CRF loss
                loss = -self.crf(logits, crf_labels, mask=mask, reduction='mean')
            else:
                # Use standard cross-entropy loss
                loss_fct = nn.CrossEntropyLoss()
                # Flatten the tokens
                active_loss = attention_mask.view(-1) == 1
                active_logits = logits.view(-1, self.num_labels)
                active_labels = torch.where(
                    active_loss,
                    labels.view(-1),
                    torch.tensor(loss_fct.ignore_index).type_as(labels)
                )
                loss = loss_fct(active_logits, active_labels)
        
        # Get predictions
        if self.crf is not None and not self.training:
            # Use CRF for prediction
            mask = attention_mask.bool()
            predictions = self.crf.decode(logits, mask=mask)
            # Convert to tensor format
            batch_size, seq_len = input_ids.shape
            pred_tensor = torch.zeros(batch_size, seq_len, dtype=torch.long, device=logits.device)
            for i, pred_seq in enumerate(predictions):
                pred_tensor[i, :len(pred_seq)] = torch.tensor(pred_seq, device=logits.device)
        else:
            predictions = torch.argmax(logits, dim=-1)
        
        if not return_dict:
            output = (logits,) + outputs[2:]
            return ((loss,) + output) if loss is not None else output
        
        return {
            'loss': loss,
            'logits': logits,
            'predictions': predictions,
            'hidden_states': outputs.hidden_states,
            'attentions': outputs.attentions,
        }
    
    def predict(
        self,
        input_ids: torch.Tensor,
        attention_mask: torch.Tensor,
        token_type_ids: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Make predictions on input data.
        
        Args:
            input_ids: Input token IDs
            attention_mask: Attention mask
            token_type_ids: Token type IDs (optional)
            
        Returns:
            Predicted labels
        """
        self.eval()
        with torch.no_grad():
            outputs = self.forward(
                input_ids=input_ids,
                attention_mask=attention_mask,
                token_type_ids=token_type_ids,
                return_dict=True
            )
            return outputs['predictions']

def create_model_and_tokenizer(
    model_name: str = "answerdotai/ModernBERT-base",
    num_labels: int = 2,
    dropout_rate: float = 0.1,
    classifier_hidden_size: Optional[int] = None,
    use_crf: bool = False
) -> Tuple[ModernBERTHallucinationDetector, AutoTokenizer]:
    """
    Create a ModernBERT hallucination detection model and tokenizer.
    
    Args:
        model_name: Name of the base ModernBERT model
        num_labels: Number of classification labels
        dropout_rate: Dropout rate for regularization
        classifier_hidden_size: Hidden size for classifier (None for single layer)
        use_crf: Whether to use CRF for sequence labeling
        
    Returns:
        Tuple of (model, tokenizer)
    """
    # Create configuration
    config = ModernBERTHallucinationDetectorConfig(
        base_model_name=model_name,
        num_labels=num_labels,
        dropout_rate=dropout_rate,
        classifier_hidden_size=classifier_hidden_size,
        use_crf=use_crf
    )
    
    # Create model
    model = ModernBERTHallucinationDetector(config)
    
    # Create tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    
    # Add special tokens if needed
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    logger.info(f"Created ModernBERT hallucination detector with {num_labels} labels")
    
    return model, tokenizer
