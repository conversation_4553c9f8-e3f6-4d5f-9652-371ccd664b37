"""
Model utilities for loading, saving, and configuring models.
"""

import torch
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import json
import logging
from transformers import AutoTokenizer

from .modernbert_classifier import (
    ModernBERTHallucinationDetector,
    ModernBERTHallucinationDetectorConfig,
    create_model_and_tokenizer
)

logger = logging.getLogger(__name__)

def save_model(
    model: ModernBERTHallucinationDetector,
    tokenizer: AutoTokenizer,
    output_dir: str,
    save_config: Optional[Dict[str, Any]] = None
) -> None:
    """
    Save model, tokenizer, and configuration to directory.
    
    Args:
        model: The trained model
        tokenizer: The tokenizer
        output_dir: Directory to save to
        save_config: Additional configuration to save
    """
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save model and tokenizer using HuggingFace methods
    model.save_pretrained(output_path)
    tokenizer.save_pretrained(output_path)
    
    # Save additional configuration
    if save_config:
        config_path = output_path / "training_config.json"
        with open(config_path, 'w') as f:
            json.dump(save_config, f, indent=2)
    
    logger.info(f"Model saved to {output_path}")

def load_model(
    model_dir: str,
    device: Optional[str] = None
) -> Tuple[ModernBERTHallucinationDetector, AutoTokenizer, Optional[Dict[str, Any]]]:
    """
    Load model, tokenizer, and configuration from directory.
    
    Args:
        model_dir: Directory to load from
        device: Device to load model on
        
    Returns:
        Tuple of (model, tokenizer, config)
    """
    model_path = Path(model_dir)
    
    if not model_path.exists():
        raise FileNotFoundError(f"Model directory not found: {model_path}")
    
    # Load model and tokenizer
    model = ModernBERTHallucinationDetector.from_pretrained(model_path)
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    
    # Load additional configuration if available
    config_path = model_path / "training_config.json"
    additional_config = None
    if config_path.exists():
        with open(config_path, 'r') as f:
            additional_config = json.load(f)
    
    # Move to device if specified
    if device:
        model = model.to(device)
    
    logger.info(f"Model loaded from {model_path}")
    
    return model, tokenizer, additional_config

def get_model_config(
    model_name: str = "answerdotai/ModernBERT-base",
    num_labels: int = 2,
    dropout_rate: float = 0.1,
    classifier_hidden_size: Optional[int] = None,
    use_crf: bool = False,
    max_length: int = 512
) -> Dict[str, Any]:
    """
    Get default model configuration.
    
    Args:
        model_name: Base model name
        num_labels: Number of classification labels
        dropout_rate: Dropout rate
        classifier_hidden_size: Hidden size for classifier
        use_crf: Whether to use CRF
        max_length: Maximum sequence length
        
    Returns:
        Configuration dictionary
    """
    return {
        'model_name': model_name,
        'num_labels': num_labels,
        'dropout_rate': dropout_rate,
        'classifier_hidden_size': classifier_hidden_size,
        'use_crf': use_crf,
        'max_length': max_length
    }

def count_parameters(model: torch.nn.Module) -> Dict[str, int]:
    """
    Count the number of parameters in a model.
    
    Args:
        model: PyTorch model
        
    Returns:
        Dictionary with parameter counts
    """
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    return {
        'total_parameters': total_params,
        'trainable_parameters': trainable_params,
        'non_trainable_parameters': total_params - trainable_params
    }

def freeze_base_model(model: ModernBERTHallucinationDetector) -> None:
    """
    Freeze the base BERT model parameters.
    
    Args:
        model: The hallucination detection model
    """
    for param in model.bert.parameters():
        param.requires_grad = False
    
    logger.info("Froze base BERT model parameters")

def unfreeze_base_model(model: ModernBERTHallucinationDetector) -> None:
    """
    Unfreeze the base BERT model parameters.
    
    Args:
        model: The hallucination detection model
    """
    for param in model.bert.parameters():
        param.requires_grad = True
    
    logger.info("Unfroze base BERT model parameters")

def get_device() -> str:
    """
    Get the best available device for training.
    
    Returns:
        Device string ('cuda', 'mps', or 'cpu')
    """
    if torch.cuda.is_available():
        return 'cuda'
    elif torch.backends.mps.is_available():
        return 'mps'
    else:
        return 'cpu'

def move_to_device(batch: Dict[str, torch.Tensor], device: str) -> Dict[str, torch.Tensor]:
    """
    Move a batch of data to the specified device.
    
    Args:
        batch: Dictionary of tensors
        device: Target device
        
    Returns:
        Dictionary of tensors moved to device
    """
    return {key: value.to(device) for key, value in batch.items()}

def print_model_summary(model: ModernBERTHallucinationDetector) -> None:
    """
    Print a summary of the model architecture.
    
    Args:
        model: The model to summarize
    """
    param_counts = count_parameters(model)
    
    print("=" * 50)
    print("Model Summary")
    print("=" * 50)
    print(f"Model type: {model.__class__.__name__}")
    print(f"Base model: {model.config.base_model_name}")
    print(f"Number of labels: {model.config.num_labels}")
    print(f"Dropout rate: {model.config.dropout_rate}")
    print(f"Use CRF: {model.config.use_crf}")
    print(f"Hidden size: {model.hidden_size}")
    print()
    print("Parameter counts:")
    print(f"  Total parameters: {param_counts['total_parameters']:,}")
    print(f"  Trainable parameters: {param_counts['trainable_parameters']:,}")
    print(f"  Non-trainable parameters: {param_counts['non_trainable_parameters']:,}")
    print("=" * 50)
