"""
Data preprocessing utilities for hallucination detection.

This module provides functions to convert the dataset format to token-level
classification format compatible with ModernBERT training.
"""

import json
import re
from typing import List, Dict, Tuple, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

def extract_hallucination_spans(hal_span_text: str) -> List[Tuple[str, int, int]]:
    """
    Extract hallucination spans from the hal_span field.
    
    Args:
        hal_span_text: Text containing <HAL>...</HAL> markers
        
    Returns:
        List of tuples (hallucinated_text, start_pos, end_pos) relative to the clean text
    """
    if not hal_span_text:
        return []
    
    spans = []
    # Find all <HAL>...</HAL> patterns
    pattern = r'<HAL>(.*?)</HAL>'
    
    # Keep track of position adjustments due to removed tags
    offset = 0
    clean_text = hal_span_text
    
    for match in re.finditer(pattern, hal_span_text):
        hallucinated_text = match.group(1)
        start_in_original = match.start()
        end_in_original = match.end()
        
        # Calculate position in clean text (without tags)
        start_in_clean = start_in_original - offset
        end_in_clean = start_in_clean + len(hallucinated_text)
        
        spans.append((hallucinated_text, start_in_clean, end_in_clean))
        
        # Update offset for next iteration
        offset += len('<HAL>') + len('</HAL>')
    
    # Remove all HAL tags to get clean text
    clean_text = re.sub(pattern, r'\1', hal_span_text)
    
    return spans, clean_text

def create_token_labels(text: str, hallucination_spans: List[Tuple[str, int, int]], 
                       tokenizer) -> Tuple[List[str], List[int]]:
    """
    Create token-level labels for hallucination detection.
    
    Args:
        text: The text to tokenize
        hallucination_spans: List of (text, start, end) tuples for hallucinated spans
        tokenizer: The tokenizer to use
        
    Returns:
        Tuple of (tokens, labels) where labels are 0 for non-hallucinated, 1 for hallucinated
    """
    # Tokenize the text
    encoding = tokenizer(text, return_offsets_mapping=True, add_special_tokens=False)
    tokens = tokenizer.convert_ids_to_tokens(encoding['input_ids'])
    offset_mapping = encoding['offset_mapping']
    
    # Initialize all labels as 0 (non-hallucinated)
    labels = [0] * len(tokens)
    
    # Mark hallucinated tokens
    for hal_text, start_pos, end_pos in hallucination_spans:
        for i, (token_start, token_end) in enumerate(offset_mapping):
            # Check if token overlaps with hallucination span
            if token_start < end_pos and token_end > start_pos:
                labels[i] = 1
    
    return tokens, labels

def preprocess_sample(sample: Dict, tokenizer, max_length: int = 512) -> Optional[Dict]:
    """
    Preprocess a single sample for token classification.
    
    Args:
        sample: Dictionary containing context, question, answer, hal, hal_span
        tokenizer: The tokenizer to use
        max_length: Maximum sequence length
        
    Returns:
        Preprocessed sample dictionary or None if processing fails
    """
    try:
        # Combine context, question, and answer
        context_text = " ".join(sample['context'])
        question_text = sample['question']
        answer_text = sample['answer']
        
        # Create the full input text
        # Format: [CLS] context [SEP] question [SEP] answer [SEP]
        full_text = f"{context_text} [SEP] {question_text} [SEP] {answer_text}"
        
        # Extract hallucination spans if present
        hallucination_spans = []
        if sample['hal'] == 1 and sample['hal_span']:
            spans, clean_answer = extract_hallucination_spans(sample['hal_span'])
            
            # Adjust span positions to account for context and question
            context_question_length = len(f"{context_text} [SEP] {question_text} [SEP] ")
            adjusted_spans = []
            for hal_text, start, end in spans:
                adjusted_start = context_question_length + start
                adjusted_end = context_question_length + end
                adjusted_spans.append((hal_text, adjusted_start, adjusted_end))
            
            hallucination_spans = adjusted_spans
        
        # Create token-level labels
        tokens, labels = create_token_labels(full_text, hallucination_spans, tokenizer)
        
        # Truncate if necessary
        if len(tokens) > max_length - 2:  # Account for [CLS] and [SEP]
            tokens = tokens[:max_length - 2]
            labels = labels[:max_length - 2]
        
        # Add special tokens
        tokens = ['[CLS]'] + tokens + ['[SEP]']
        labels = [0] + labels + [0]  # Special tokens are never hallucinated
        
        # Convert to input IDs
        input_ids = tokenizer.convert_tokens_to_ids(tokens)
        
        # Create attention mask
        attention_mask = [1] * len(input_ids)
        
        # Pad to max_length
        padding_length = max_length - len(input_ids)
        if padding_length > 0:
            input_ids.extend([tokenizer.pad_token_id] * padding_length)
            attention_mask.extend([0] * padding_length)
            labels.extend([-100] * padding_length)  # -100 is ignored in loss calculation
        
        return {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'labels': labels,
            'original_hal_label': sample['hal'],
            'text': full_text
        }
        
    except Exception as e:
        logger.error(f"Error preprocessing sample: {e}")
        return None

def preprocess_dataset(data: List[Dict], tokenizer, max_length: int = 512) -> List[Dict]:
    """
    Preprocess the entire dataset for token classification.
    
    Args:
        data: List of sample dictionaries
        tokenizer: The tokenizer to use
        max_length: Maximum sequence length
        
    Returns:
        List of preprocessed samples
    """
    preprocessed_data = []
    
    for i, sample in enumerate(data):
        if i % 100 == 0:
            logger.info(f"Processing sample {i}/{len(data)}")
        
        processed_sample = preprocess_sample(sample, tokenizer, max_length)
        if processed_sample is not None:
            preprocessed_data.append(processed_sample)
    
    logger.info(f"Successfully preprocessed {len(preprocessed_data)}/{len(data)} samples")
    return preprocessed_data

def save_preprocessed_data(data: List[Dict], output_path: str):
    """Save preprocessed data to a JSON file."""
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2)
    logger.info(f"Saved preprocessed data to {output_path}")

def load_preprocessed_data(input_path: str) -> List[Dict]:
    """Load preprocessed data from a JSON file."""
    with open(input_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    logger.info(f"Loaded preprocessed data from {input_path}")
    return data
