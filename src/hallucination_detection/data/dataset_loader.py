"""
Dataset loading utilities for hallucination detection.
"""

import json
import torch
from torch.utils.data import Dataset, DataLoader
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class HallucinationDataset(Dataset):
    """
    PyTorch Dataset for hallucination detection.
    """
    
    def __init__(self, data: List[Dict]):
        """
        Initialize the dataset.
        
        Args:
            data: List of preprocessed sample dictionaries
        """
        self.data = data
    
    def __len__(self) -> int:
        return len(self.data)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        Get a single sample.
        
        Args:
            idx: Sample index
            
        Returns:
            Dictionary containing input_ids, attention_mask, and labels as tensors
        """
        sample = self.data[idx]
        
        return {
            'input_ids': torch.tensor(sample['input_ids'], dtype=torch.long),
            'attention_mask': torch.tensor(sample['attention_mask'], dtype=torch.long),
            'labels': torch.tensor(sample['labels'], dtype=torch.long),
        }

def load_dataset_with_hal(file_path: str) -> List[Dict]:
    """
    Load the dataset_with_hal.jsonl file.
    
    Args:
        file_path: Path to the JSONL file
        
    Returns:
        List of sample dictionaries
    """
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line.strip()))
    
    logger.info(f"Loaded {len(data)} samples from {file_path}")
    return data

def load_ragtruth_dataset(file_path: str) -> List[Dict]:
    """
    Load the RAGTruth dataset (converted format).

    Args:
        file_path: Path to the converted RAGTruth JSONL file

    Returns:
        List of sample dictionaries in our format
    """
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                data.append(json.loads(line.strip()))

        logger.info(f"Loaded {len(data)} samples from RAGTruth dataset: {file_path}")
        return data
    except FileNotFoundError:
        logger.error(f"RAGTruth dataset file not found: {file_path}")
        logger.info("Run 'python scripts/download_ragtruth.py' to download the dataset")
        return []

def split_dataset(data: List[Dict], train_ratio: float = 0.8, 
                 val_ratio: float = 0.1, random_seed: int = 42) -> Tuple[List[Dict], List[Dict], List[Dict]]:
    """
    Split dataset into train, validation, and test sets.
    
    Args:
        data: List of sample dictionaries
        train_ratio: Ratio of data for training
        val_ratio: Ratio of data for validation
        random_seed: Random seed for reproducibility
        
    Returns:
        Tuple of (train_data, val_data, test_data)
    """
    import random
    random.seed(random_seed)
    
    # Shuffle data
    shuffled_data = data.copy()
    random.shuffle(shuffled_data)
    
    # Calculate split indices
    total_samples = len(shuffled_data)
    train_end = int(total_samples * train_ratio)
    val_end = train_end + int(total_samples * val_ratio)
    
    # Split data
    train_data = shuffled_data[:train_end]
    val_data = shuffled_data[train_end:val_end]
    test_data = shuffled_data[val_end:]
    
    logger.info(f"Dataset split: {len(train_data)} train, {len(val_data)} val, {len(test_data)} test")
    
    return train_data, val_data, test_data

def create_dataloaders(train_data: List[Dict], val_data: List[Dict], test_data: List[Dict],
                      batch_size: int = 16, num_workers: int = 4) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    Create PyTorch DataLoaders for train, validation, and test sets.
    
    Args:
        train_data: Training data
        val_data: Validation data
        test_data: Test data
        batch_size: Batch size for training
        num_workers: Number of worker processes for data loading
        
    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    # Create datasets
    train_dataset = HallucinationDataset(train_data)
    val_dataset = HallucinationDataset(val_data)
    test_dataset = HallucinationDataset(test_data)
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    logger.info(f"Created DataLoaders with batch_size={batch_size}")
    
    return train_loader, val_loader, test_loader

def get_class_weights(data: List[Dict]) -> torch.Tensor:
    """
    Calculate class weights for handling class imbalance.
    
    Args:
        data: List of preprocessed sample dictionaries
        
    Returns:
        Tensor of class weights
    """
    # Count tokens for each class
    class_counts = [0, 0]  # [non-hallucinated, hallucinated]
    
    for sample in data:
        labels = sample['labels']
        for label in labels:
            if label != -100:  # Ignore padding tokens
                class_counts[label] += 1
    
    # Calculate weights (inverse frequency)
    total_tokens = sum(class_counts)
    weights = [total_tokens / (2 * count) for count in class_counts]
    
    logger.info(f"Class distribution: {class_counts}")
    logger.info(f"Class weights: {weights}")
    
    return torch.tensor(weights, dtype=torch.float32)
