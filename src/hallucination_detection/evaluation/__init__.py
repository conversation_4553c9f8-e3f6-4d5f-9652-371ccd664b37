"""Evaluation utilities and metrics."""

from .evaluator import HallucinationEvaluator
from .metrics import (
    compute_token_level_metrics,
    compute_span_level_metrics,
    compute_example_level_metrics,
    compute_comparative_metrics
)
from .visualization import plot_confusion_matrix, plot_performance_comparison

__all__ = [
    "HallucinationEvaluator",
    "compute_token_level_metrics",
    "compute_span_level_metrics", 
    "compute_example_level_metrics",
    "compute_comparative_metrics",
    "plot_confusion_matrix",
    "plot_performance_comparison"
]
