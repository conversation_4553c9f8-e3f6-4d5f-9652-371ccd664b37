"""
Comprehensive evaluation framework for hallucination detection models.
"""

import torch
from torch.utils.data import DataLoader
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import logging
from pathlib import Path
import json
from tqdm import tqdm

from ..models.modernbert_classifier import ModernBERTHallucinationDetector
from ..models.model_utils import move_to_device
from ..training.metrics import (
    compute_token_metrics,
    compute_span_metrics,
    compute_example_metrics,
    compute_comprehensive_metrics,
    tokens_to_spans,
    get_classification_report,
    get_confusion_matrix
)

logger = logging.getLogger(__name__)

class HallucinationEvaluator:
    """
    Comprehensive evaluator for hallucination detection models.
    """
    
    def __init__(
        self,
        model: ModernBERTHallucinationDetector,
        tokenizer,
        device: Optional[str] = None
    ):
        """
        Initialize the evaluator.
        
        Args:
            model: The trained model
            tokenizer: The tokenizer
            device: Device to use for evaluation
        """
        self.model = model
        self.tokenizer = tokenizer
        self.device = device if device else ('cuda' if torch.cuda.is_available() else 'cpu')
        
        self.model = self.model.to(self.device)
        self.model.eval()
        
        logger.info(f"Evaluator initialized with device: {self.device}")
    
    def evaluate_dataloader(
        self,
        dataloader: DataLoader,
        compute_spans: bool = True,
        save_predictions: bool = False,
        output_file: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Evaluate model on a data loader.
        
        Args:
            dataloader: Data loader to evaluate on
            compute_spans: Whether to compute span-level metrics
            save_predictions: Whether to save predictions
            output_file: File to save predictions to
            
        Returns:
            Dictionary of evaluation results
        """
        all_token_predictions = []
        all_token_labels = []
        all_example_predictions = []
        all_example_labels = []
        all_span_predictions = []
        all_span_labels = []
        all_losses = []
        
        predictions_data = []
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(dataloader, desc="Evaluating")):
                batch = move_to_device(batch, self.device)
                
                # Forward pass
                outputs = self.model(**batch)
                loss = outputs['loss']
                predictions = outputs['predictions']
                
                all_losses.append(loss.item())
                
                # Process each example in the batch
                for i in range(predictions.shape[0]):
                    # Get attention mask and labels
                    attention_mask = batch['attention_mask'][i]
                    labels = batch['labels'][i]
                    preds = predictions[i]
                    
                    # Get valid tokens (not padding)
                    valid_length = attention_mask.sum().item()
                    valid_labels = labels[:valid_length].cpu().numpy()
                    valid_preds = preds[:valid_length].cpu().numpy()
                    
                    # Filter out special tokens and padding
                    token_mask = valid_labels != -100
                    if token_mask.any():
                        token_labels = valid_labels[token_mask]
                        token_preds = valid_preds[token_mask]
                        
                        all_token_labels.extend(token_labels)
                        all_token_predictions.extend(token_preds)
                        
                        # Example-level prediction (any hallucinated token)
                        example_label = 1 if (token_labels == 1).any() else 0
                        example_pred = 1 if (token_preds == 1).any() else 0
                        
                        all_example_labels.append(example_label)
                        all_example_predictions.append(example_pred)
                        
                        # Span-level predictions
                        if compute_spans:
                            # Convert tokens back to text for span computation
                            input_ids = batch['input_ids'][i][:valid_length]
                            tokens = self.tokenizer.convert_ids_to_tokens(input_ids)
                            
                            # Get spans
                            true_spans = tokens_to_spans(token_labels, tokens)
                            pred_spans = tokens_to_spans(token_preds, tokens)
                            
                            all_span_labels.append(true_spans)
                            all_span_predictions.append(pred_spans)
                        
                        # Save prediction data if requested
                        if save_predictions:
                            pred_data = {
                                'batch_idx': batch_idx,
                                'example_idx': i,
                                'tokens': tokens if compute_spans else None,
                                'token_labels': token_labels.tolist(),
                                'token_predictions': token_preds.tolist(),
                                'example_label': example_label,
                                'example_prediction': example_pred,
                                'loss': loss.item()
                            }
                            
                            if compute_spans:
                                pred_data['true_spans'] = true_spans
                                pred_data['pred_spans'] = pred_spans
                            
                            predictions_data.append(pred_data)
        
        # Compute metrics
        results = {}
        
        # Average loss
        results['loss'] = np.mean(all_losses)
        
        # Token-level metrics
        token_metrics = compute_token_metrics(all_token_labels, all_token_predictions)
        results.update({f'token_{k}': v for k, v in token_metrics.items()})
        
        # Example-level metrics
        example_metrics = compute_example_metrics(all_example_labels, all_example_predictions)
        results.update({f'example_{k}': v for k, v in example_metrics.items()})
        
        # Span-level metrics
        if compute_spans and all_span_labels:
            span_metrics = compute_span_metrics(all_span_labels, all_span_predictions)
            results.update(span_metrics)
        
        # Classification report
        results['token_classification_report'] = get_classification_report(
            all_token_labels, all_token_predictions
        )
        results['example_classification_report'] = get_classification_report(
            all_example_labels, all_example_predictions
        )
        
        # Confusion matrices
        results['token_confusion_matrix'] = get_confusion_matrix(
            all_token_labels, all_token_predictions
        ).tolist()
        results['example_confusion_matrix'] = get_confusion_matrix(
            all_example_labels, all_example_predictions
        ).tolist()
        
        # Save predictions if requested
        if save_predictions and output_file:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w') as f:
                json.dump({
                    'predictions': predictions_data,
                    'metrics': results
                }, f, indent=2)
            
            logger.info(f"Predictions saved to {output_path}")
        
        return results
    
    def compare_models(
        self,
        other_evaluator: 'HallucinationEvaluator',
        dataloader: DataLoader,
        model_names: Tuple[str, str] = ("Model 1", "Model 2")
    ) -> Dict[str, Any]:
        """
        Compare two models on the same dataset.
        
        Args:
            other_evaluator: Another evaluator to compare with
            dataloader: Data loader to evaluate on
            model_names: Names for the two models
            
        Returns:
            Comparison results
        """
        logger.info(f"Comparing {model_names[0]} vs {model_names[1]}")
        
        # Evaluate both models
        results1 = self.evaluate_dataloader(dataloader, compute_spans=True)
        results2 = other_evaluator.evaluate_dataloader(dataloader, compute_spans=True)
        
        # Create comparison
        comparison = {
            'model_names': model_names,
            'model1_results': results1,
            'model2_results': results2,
            'comparison': {}
        }
        
        # Compare key metrics
        key_metrics = [
            'token_f1', 'token_precision', 'token_recall',
            'example_f1', 'example_precision', 'example_recall',
            'span_f1', 'span_precision', 'span_recall'
        ]
        
        for metric in key_metrics:
            if metric in results1 and metric in results2:
                diff = results1[metric] - results2[metric]
                comparison['comparison'][f'{metric}_diff'] = diff
                comparison['comparison'][f'{metric}_better'] = model_names[0] if diff > 0 else model_names[1]
        
        return comparison
    
    def evaluate_multiple_datasets(
        self,
        datasets: Dict[str, DataLoader],
        output_dir: str = "evaluation_results"
    ) -> Dict[str, Dict[str, Any]]:
        """
        Evaluate model on multiple datasets.
        
        Args:
            datasets: Dictionary of dataset name -> DataLoader
            output_dir: Directory to save results
            
        Returns:
            Dictionary of dataset name -> results
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        all_results = {}
        
        for dataset_name, dataloader in datasets.items():
            logger.info(f"Evaluating on {dataset_name}")
            
            results = self.evaluate_dataloader(
                dataloader,
                compute_spans=True,
                save_predictions=True,
                output_file=output_path / f"{dataset_name}_predictions.json"
            )
            
            all_results[dataset_name] = results
            
            # Save individual results
            with open(output_path / f"{dataset_name}_results.json", 'w') as f:
                json.dump(results, f, indent=2)
        
        # Save combined results
        with open(output_path / "all_results.json", 'w') as f:
            json.dump(all_results, f, indent=2)
        
        # Create summary
        summary = self._create_summary(all_results)
        with open(output_path / "summary.json", 'w') as f:
            json.dump(summary, f, indent=2)
        
        logger.info(f"Evaluation results saved to {output_path}")
        
        return all_results
    
    def _create_summary(self, all_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Create a summary of results across datasets."""
        summary = {
            'datasets': list(all_results.keys()),
            'metrics_summary': {}
        }
        
        # Key metrics to summarize
        key_metrics = [
            'token_f1', 'token_precision', 'token_recall',
            'example_f1', 'example_precision', 'example_recall',
            'span_f1', 'span_precision', 'span_recall'
        ]
        
        for metric in key_metrics:
            values = []
            for dataset_name, results in all_results.items():
                if metric in results:
                    values.append(results[metric])
            
            if values:
                summary['metrics_summary'][metric] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'values': dict(zip(all_results.keys(), values))
                }
        
        return summary
