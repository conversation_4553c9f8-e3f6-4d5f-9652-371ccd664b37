"""
Metrics computation for hallucination detection.
"""

import numpy as np
from sklearn.metrics import (
    accuracy_score, 
    precision_recall_fscore_support,
    classification_report,
    confusion_matrix
)
from typing import List, Dict, Tuple, Any
import logging

logger = logging.getLogger(__name__)

def compute_token_metrics(y_true: List[int], y_pred: List[int]) -> Dict[str, float]:
    """
    Compute token-level metrics for binary classification.
    
    Args:
        y_true: True labels
        y_pred: Predicted labels
        
    Returns:
        Dictionary of metrics
    """
    # Convert to numpy arrays
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    
    # Compute basic metrics
    accuracy = accuracy_score(y_true, y_pred)
    precision, recall, f1, support = precision_recall_fscore_support(
        y_true, y_pred, average='binary', pos_label=1, zero_division=0
    )
    
    # Compute macro averages
    precision_macro, recall_macro, f1_macro, _ = precision_recall_fscore_support(
        y_true, y_pred, average='macro', zero_division=0
    )
    
    # Compute per-class metrics
    precision_per_class, recall_per_class, f1_per_class, support_per_class = precision_recall_fscore_support(
        y_true, y_pred, average=None, zero_division=0
    )
    
    metrics = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'precision_macro': precision_macro,
        'recall_macro': recall_macro,
        'f1_macro': f1_macro,
        'support_positive': int(support),
        'support_total': len(y_true)
    }
    
    # Add per-class metrics
    for i, (p, r, f, s) in enumerate(zip(precision_per_class, recall_per_class, f1_per_class, support_per_class)):
        metrics[f'precision_class_{i}'] = p
        metrics[f'recall_class_{i}'] = r
        metrics[f'f1_class_{i}'] = f
        metrics[f'support_class_{i}'] = int(s)
    
    return metrics

def compute_span_metrics(y_true_spans: List[List[Tuple[int, int]]], 
                        y_pred_spans: List[List[Tuple[int, int]]]) -> Dict[str, float]:
    """
    Compute span-level metrics for hallucination detection.
    
    Args:
        y_true_spans: List of true span lists for each example
        y_pred_spans: List of predicted span lists for each example
        
    Returns:
        Dictionary of span-level metrics
    """
    total_true_spans = 0
    total_pred_spans = 0
    total_correct_spans = 0
    total_partial_matches = 0
    
    for true_spans, pred_spans in zip(y_true_spans, y_pred_spans):
        true_spans_set = set(true_spans)
        pred_spans_set = set(pred_spans)
        
        total_true_spans += len(true_spans_set)
        total_pred_spans += len(pred_spans_set)
        
        # Exact matches
        exact_matches = true_spans_set.intersection(pred_spans_set)
        total_correct_spans += len(exact_matches)
        
        # Partial matches (overlapping spans)
        for true_span in true_spans_set:
            if true_span not in exact_matches:
                for pred_span in pred_spans_set:
                    if pred_span not in exact_matches:
                        # Check for overlap
                        if spans_overlap(true_span, pred_span):
                            total_partial_matches += 1
                            break
    
    # Compute metrics
    precision = total_correct_spans / total_pred_spans if total_pred_spans > 0 else 0.0
    recall = total_correct_spans / total_true_spans if total_true_spans > 0 else 0.0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
    
    # Partial match metrics
    partial_precision = (total_correct_spans + total_partial_matches) / total_pred_spans if total_pred_spans > 0 else 0.0
    partial_recall = (total_correct_spans + total_partial_matches) / total_true_spans if total_true_spans > 0 else 0.0
    partial_f1 = 2 * partial_precision * partial_recall / (partial_precision + partial_recall) if (partial_precision + partial_recall) > 0 else 0.0
    
    return {
        'span_precision': precision,
        'span_recall': recall,
        'span_f1': f1,
        'span_precision_partial': partial_precision,
        'span_recall_partial': partial_recall,
        'span_f1_partial': partial_f1,
        'total_true_spans': total_true_spans,
        'total_pred_spans': total_pred_spans,
        'total_correct_spans': total_correct_spans,
        'total_partial_matches': total_partial_matches
    }

def spans_overlap(span1: Tuple[int, int], span2: Tuple[int, int]) -> bool:
    """
    Check if two spans overlap.
    
    Args:
        span1: First span (start, end)
        span2: Second span (start, end)
        
    Returns:
        True if spans overlap, False otherwise
    """
    start1, end1 = span1
    start2, end2 = span2
    return start1 < end2 and start2 < end1

def compute_example_metrics(y_true_labels: List[int], y_pred_labels: List[int]) -> Dict[str, float]:
    """
    Compute example-level metrics (whether entire example is hallucinated).
    
    Args:
        y_true_labels: True example-level labels (0 or 1)
        y_pred_labels: Predicted example-level labels (0 or 1)
        
    Returns:
        Dictionary of example-level metrics
    """
    return compute_token_metrics(y_true_labels, y_pred_labels)

def tokens_to_spans(token_labels: List[int], tokens: List[str]) -> List[Tuple[int, int]]:
    """
    Convert token-level labels to span-level labels.
    
    Args:
        token_labels: List of token labels (0 or 1)
        tokens: List of tokens
        
    Returns:
        List of (start, end) tuples for hallucinated spans
    """
    spans = []
    start = None
    
    for i, (label, token) in enumerate(zip(token_labels, tokens)):
        if label == 1:  # Hallucinated token
            if start is None:
                start = i
        else:  # Non-hallucinated token
            if start is not None:
                spans.append((start, i))
                start = None
    
    # Handle case where sequence ends with hallucinated tokens
    if start is not None:
        spans.append((start, len(tokens)))
    
    return spans

def get_classification_report(y_true: List[int], y_pred: List[int], 
                            target_names: List[str] = None) -> str:
    """
    Get detailed classification report.
    
    Args:
        y_true: True labels
        y_pred: Predicted labels
        target_names: Names for the classes
        
    Returns:
        Classification report as string
    """
    if target_names is None:
        target_names = ['Non-hallucinated', 'Hallucinated']
    
    return classification_report(y_true, y_pred, target_names=target_names, zero_division=0)

def get_confusion_matrix(y_true: List[int], y_pred: List[int]) -> np.ndarray:
    """
    Get confusion matrix.
    
    Args:
        y_true: True labels
        y_pred: Predicted labels
        
    Returns:
        Confusion matrix as numpy array
    """
    return confusion_matrix(y_true, y_pred)

def compute_comprehensive_metrics(
    token_true: List[int],
    token_pred: List[int],
    example_true: List[int],
    example_pred: List[int],
    span_true: List[List[Tuple[int, int]]] = None,
    span_pred: List[List[Tuple[int, int]]] = None
) -> Dict[str, Any]:
    """
    Compute comprehensive metrics at all levels.
    
    Args:
        token_true: True token-level labels
        token_pred: Predicted token-level labels
        example_true: True example-level labels
        example_pred: Predicted example-level labels
        span_true: True span-level labels (optional)
        span_pred: Predicted span-level labels (optional)
        
    Returns:
        Dictionary containing all metrics
    """
    metrics = {}
    
    # Token-level metrics
    token_metrics = compute_token_metrics(token_true, token_pred)
    metrics.update({f'token_{k}': v for k, v in token_metrics.items()})
    
    # Example-level metrics
    example_metrics = compute_example_metrics(example_true, example_pred)
    metrics.update({f'example_{k}': v for k, v in example_metrics.items()})
    
    # Span-level metrics (if provided)
    if span_true is not None and span_pred is not None:
        span_metrics = compute_span_metrics(span_true, span_pred)
        metrics.update(span_metrics)
    
    return metrics
