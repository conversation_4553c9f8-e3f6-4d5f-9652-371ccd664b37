"""
Training utilities for hallucination detection models.
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.optim import AdamW
from torch.optim.lr_scheduler import LinearLR, CosineAnnealingLR
from transformers import get_linear_schedule_with_warmup
from typing import Dict, List, Optional, Tuple, Any
import logging
from pathlib import Path
import json
from tqdm import tqdm
import wandb
from datetime import datetime

from ..models.modernbert_classifier import ModernBERTHallucinationDetector
from ..models.model_utils import save_model, get_device, move_to_device
from .metrics import compute_token_metrics

logger = logging.getLogger(__name__)

class HallucinationTrainer:
    """
    Trainer class for hallucination detection models.
    """
    
    def __init__(
        self,
        model: ModernBERTHallucinationDetector,
        tokenizer,
        train_dataloader: DataLoader,
        val_dataloader: DataLoader,
        test_dataloader: Optional[DataLoader] = None,
        learning_rate: float = 2e-5,
        weight_decay: float = 0.01,
        warmup_steps: int = 0,
        max_grad_norm: float = 1.0,
        output_dir: str = "outputs",
        logging_steps: int = 100,
        save_steps: int = 500,
        eval_steps: int = 500,
        use_wandb: bool = False,
        wandb_project: str = "hallucination-detection",
        device: Optional[str] = None
    ):
        """
        Initialize the trainer.
        
        Args:
            model: The model to train
            tokenizer: The tokenizer
            train_dataloader: Training data loader
            val_dataloader: Validation data loader
            test_dataloader: Test data loader (optional)
            learning_rate: Learning rate
            weight_decay: Weight decay for optimizer
            warmup_steps: Number of warmup steps
            max_grad_norm: Maximum gradient norm for clipping
            output_dir: Directory to save outputs
            logging_steps: Steps between logging
            save_steps: Steps between saving checkpoints
            eval_steps: Steps between evaluations
            use_wandb: Whether to use Weights & Biases logging
            wandb_project: W&B project name
            device: Device to use for training
        """
        self.model = model
        self.tokenizer = tokenizer
        self.train_dataloader = train_dataloader
        self.val_dataloader = val_dataloader
        self.test_dataloader = test_dataloader
        
        self.learning_rate = learning_rate
        self.weight_decay = weight_decay
        self.warmup_steps = warmup_steps
        self.max_grad_norm = max_grad_norm
        
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logging_steps = logging_steps
        self.save_steps = save_steps
        self.eval_steps = eval_steps
        
        self.use_wandb = use_wandb
        self.wandb_project = wandb_project
        
        # Set device
        self.device = device if device else get_device()
        self.model = self.model.to(self.device)
        
        # Initialize optimizer and scheduler
        self.optimizer = None
        self.scheduler = None
        
        # Training state
        self.global_step = 0
        self.epoch = 0
        self.best_val_f1 = 0.0
        self.training_history = []
        
        logger.info(f"Trainer initialized with device: {self.device}")
    
    def setup_optimizer_and_scheduler(self, num_training_steps: int):
        """
        Setup optimizer and learning rate scheduler.
        
        Args:
            num_training_steps: Total number of training steps
        """
        # Create optimizer
        no_decay = ["bias", "LayerNorm.weight"]
        optimizer_grouped_parameters = [
            {
                "params": [p for n, p in self.model.named_parameters() 
                          if not any(nd in n for nd in no_decay)],
                "weight_decay": self.weight_decay,
            },
            {
                "params": [p for n, p in self.model.named_parameters() 
                          if any(nd in n for nd in no_decay)],
                "weight_decay": 0.0,
            },
        ]
        
        self.optimizer = AdamW(
            optimizer_grouped_parameters,
            lr=self.learning_rate,
            eps=1e-8
        )
        
        # Create scheduler
        if self.warmup_steps > 0:
            self.scheduler = get_linear_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=self.warmup_steps,
                num_training_steps=num_training_steps
            )
        else:
            self.scheduler = None
        
        logger.info(f"Optimizer and scheduler setup complete")
    
    def train_epoch(self) -> Dict[str, float]:
        """
        Train for one epoch.
        
        Returns:
            Dictionary of training metrics
        """
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        progress_bar = tqdm(self.train_dataloader, desc=f"Epoch {self.epoch}")
        
        for batch in progress_bar:
            # Move batch to device
            batch = move_to_device(batch, self.device)
            
            # Forward pass
            outputs = self.model(**batch)
            loss = outputs['loss']
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            if self.max_grad_norm > 0:
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.max_grad_norm)
            
            # Optimizer step
            self.optimizer.step()
            if self.scheduler:
                self.scheduler.step()
            self.optimizer.zero_grad()
            
            # Update metrics
            total_loss += loss.item()
            num_batches += 1
            self.global_step += 1
            
            # Update progress bar
            progress_bar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'avg_loss': f"{total_loss / num_batches:.4f}",
                'lr': f"{self.optimizer.param_groups[0]['lr']:.2e}"
            })
            
            # Logging
            if self.global_step % self.logging_steps == 0:
                self.log_metrics({
                    'train/loss': loss.item(),
                    'train/learning_rate': self.optimizer.param_groups[0]['lr'],
                    'train/epoch': self.epoch,
                    'train/global_step': self.global_step
                })
            
            # Evaluation
            if self.global_step % self.eval_steps == 0:
                val_metrics = self.evaluate()
                self.log_metrics(val_metrics)
                
                # Save best model
                if val_metrics.get('val/f1', 0) > self.best_val_f1:
                    self.best_val_f1 = val_metrics['val/f1']
                    self.save_checkpoint(is_best=True)
            
            # Save checkpoint
            if self.global_step % self.save_steps == 0:
                self.save_checkpoint()
        
        avg_loss = total_loss / num_batches
        return {'train/avg_loss': avg_loss}
    
    def evaluate(self, dataloader: Optional[DataLoader] = None) -> Dict[str, float]:
        """
        Evaluate the model.
        
        Args:
            dataloader: Data loader to evaluate on (defaults to validation)
            
        Returns:
            Dictionary of evaluation metrics
        """
        if dataloader is None:
            dataloader = self.val_dataloader
        
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for batch in tqdm(dataloader, desc="Evaluating"):
                batch = move_to_device(batch, self.device)
                
                outputs = self.model(**batch)
                loss = outputs['loss']
                predictions = outputs['predictions']
                
                total_loss += loss.item()
                
                # Collect predictions and labels
                mask = batch['attention_mask'].bool()
                labels = batch['labels']
                
                for i in range(predictions.shape[0]):
                    seq_len = mask[i].sum().item()
                    pred_seq = predictions[i][:seq_len].cpu().numpy()
                    label_seq = labels[i][:seq_len].cpu().numpy()
                    
                    # Filter out special tokens and padding
                    valid_indices = label_seq != -100
                    if valid_indices.any():
                        all_predictions.extend(pred_seq[valid_indices])
                        all_labels.extend(label_seq[valid_indices])
        
        # Compute metrics
        avg_loss = total_loss / len(dataloader)
        metrics = compute_token_metrics(all_labels, all_predictions)
        
        # Add loss to metrics
        prefix = "val" if dataloader == self.val_dataloader else "test"
        result_metrics = {f"{prefix}/loss": avg_loss}
        result_metrics.update({f"{prefix}/{k}": v for k, v in metrics.items()})
        
        self.model.train()
        return result_metrics
    
    def train(self, num_epochs: int):
        """
        Train the model for the specified number of epochs.
        
        Args:
            num_epochs: Number of epochs to train
        """
        # Calculate total training steps
        num_training_steps = len(self.train_dataloader) * num_epochs
        
        # Setup optimizer and scheduler
        self.setup_optimizer_and_scheduler(num_training_steps)
        
        # Initialize W&B if requested
        if self.use_wandb:
            wandb.init(
                project=self.wandb_project,
                config={
                    'learning_rate': self.learning_rate,
                    'weight_decay': self.weight_decay,
                    'warmup_steps': self.warmup_steps,
                    'num_epochs': num_epochs,
                    'batch_size': self.train_dataloader.batch_size,
                    'model_name': self.model.config.base_model_name,
                    'num_labels': self.model.config.num_labels,
                }
            )
        
        logger.info(f"Starting training for {num_epochs} epochs")
        logger.info(f"Total training steps: {num_training_steps}")
        
        for epoch in range(num_epochs):
            self.epoch = epoch
            
            # Train epoch
            train_metrics = self.train_epoch()
            
            # Evaluate
            val_metrics = self.evaluate()
            
            # Log epoch metrics
            epoch_metrics = {**train_metrics, **val_metrics}
            self.log_metrics(epoch_metrics)
            self.training_history.append(epoch_metrics)
            
            logger.info(f"Epoch {epoch}: {epoch_metrics}")
        
        # Final evaluation on test set if available
        if self.test_dataloader:
            test_metrics = self.evaluate(self.test_dataloader)
            self.log_metrics(test_metrics)
            logger.info(f"Test results: {test_metrics}")
        
        # Save final model
        self.save_checkpoint(is_final=True)
        
        if self.use_wandb:
            wandb.finish()
        
        logger.info("Training completed")
    
    def log_metrics(self, metrics: Dict[str, float]):
        """Log metrics to W&B and/or console."""
        if self.use_wandb:
            wandb.log(metrics, step=self.global_step)
    
    def save_checkpoint(self, is_best: bool = False, is_final: bool = False):
        """
        Save model checkpoint.
        
        Args:
            is_best: Whether this is the best model so far
            is_final: Whether this is the final model
        """
        if is_best:
            checkpoint_dir = self.output_dir / "best_model"
        elif is_final:
            checkpoint_dir = self.output_dir / "final_model"
        else:
            checkpoint_dir = self.output_dir / f"checkpoint-{self.global_step}"
        
        # Save model and tokenizer
        save_config = {
            'global_step': self.global_step,
            'epoch': self.epoch,
            'best_val_f1': self.best_val_f1,
            'learning_rate': self.learning_rate,
            'training_history': self.training_history
        }
        
        save_model(self.model, self.tokenizer, str(checkpoint_dir), save_config)
        
        if is_best:
            logger.info(f"Saved best model (F1: {self.best_val_f1:.4f}) to {checkpoint_dir}")
        else:
            logger.info(f"Saved checkpoint to {checkpoint_dir}")
