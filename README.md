# ModernBERT Hallucination Detection

A comprehensive framework for fine-tuning ModernBERT models for hallucination detection in Retrieval-Augmented Generation (RAG) systems. This project implements token-level classification to identify hallucinated content in LLM responses.

## Features

- **ModernBERT Integration**: Fine-tune ModernBERT-base for token-level hallucination detection
- **Multi-Dataset Support**: Train and evaluate on multiple datasets including your custom dataset and RAGTruth
- **Comprehensive Evaluation**: Token-level, span-level, and example-level metrics
- **Comparative Analysis**: Compare performance across different datasets and model configurations
- **Statistical Testing**: Built-in statistical significance testing for model comparisons
- **Error Analysis**: Detailed analysis of model errors and failure cases

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd modernbert-hallucination-detection
```

2. Install dependencies:
```bash
pip install -r requirements.txt
# or
pip install -e .
```

3. For development:
```bash
pip install -e .[dev]
```

## Quick Start

### 1. Analyze Your Dataset

First, analyze your dataset structure:

```bash
python scripts/simple_dataset_analysis.py
```

### 2. Download RAGTruth Dataset (Optional)

To use RAGTruth as a baseline comparison:

```bash
python scripts/download_ragtruth.py
```

### 3. Train the Model

Train on your primary dataset:

```bash
python scripts/train_model.py \
    --primary_dataset data/dataset_with_hal.jsonl \
    --output_dir outputs/primary_only \
    --num_epochs 3 \
    --batch_size 16 \
    --learning_rate 2e-5
```

Train with RAGTruth comparison:

```bash
python scripts/train_model.py \
    --primary_dataset data/dataset_with_hal.jsonl \
    --use_ragtruth \
    --output_dir outputs/with_ragtruth \
    --num_epochs 3 \
    --batch_size 16 \
    --learning_rate 2e-5
```

### 4. Evaluate the Model

Comprehensive evaluation:

```bash
python scripts/evaluate_model.py \
    --model_dir outputs/primary_only/best_model \
    --primary_dataset data/dataset_with_hal.jsonl \
    --evaluate_ragtruth \
    --output_dir evaluation_results \
    --statistical_tests \
    --error_analysis
```

Compare two models:

```bash
python scripts/evaluate_model.py \
    --model_dir outputs/primary_only/best_model \
    --comparison_model_dir outputs/with_ragtruth/best_model \
    --primary_dataset data/dataset_with_hal.jsonl \
    --evaluate_ragtruth \
    --output_dir comparison_results \
    --statistical_tests
```

## Dataset Format

Your dataset should be in JSONL format with the following structure:

```json
{
  "context": ["List of context passages"],
  "question": "The question being asked",
  "answer": "The model's response",
  "hal": 0,  // 0 for non-hallucinated, 1 for hallucinated
  "hal_span": ""  // For hallucinated examples, use <HAL>text</HAL> to mark spans
}
```

Example hallucinated entry:
```json
{
  "context": ["France is in Europe. Paris is the capital."],
  "question": "What is the population of France?",
  "answer": "The population of France is 70 million people.",
  "hal": 1,
  "hal_span": "The population of France is <HAL>70 million</HAL> people."
}
```

## Project Structure

```
├── src/hallucination_detection/
│   ├── models/              # Model definitions
│   ├── data/                # Data loading and preprocessing
│   ├── training/            # Training utilities
│   ├── evaluation/          # Evaluation framework
│   └── utils/               # Utility functions
├── scripts/                 # Training and evaluation scripts
├── configs/                 # Configuration files
├── data/                    # Dataset storage
├── outputs/                 # Training outputs
└── evaluation_results/      # Evaluation results
```

## Configuration

You can use YAML configuration files for easier experiment management:

```yaml
# configs/experiment.yaml
data:
  primary_dataset: "data/dataset_with_hal.jsonl"
  use_ragtruth: true
  max_length: 512

model:
  name: "answerdotai/ModernBERT-base"
  dropout_rate: 0.1
  use_crf: false

training:
  num_epochs: 5
  batch_size: 16
  learning_rate: 2e-5
```

## Advanced Features

### Using CRF for Sequence Labeling

Enable Conditional Random Fields for better sequence labeling:

```bash
python scripts/train_model.py \
    --use_crf \
    --primary_dataset data/dataset_with_hal.jsonl
```

### Weights & Biases Integration

Track experiments with W&B:

```bash
python scripts/train_model.py \
    --use_wandb \
    --wandb_project my-hallucination-project \
    --primary_dataset data/dataset_with_hal.jsonl
```

### Custom Model Architecture

Use a custom classifier head:

```bash
python scripts/train_model.py \
    --classifier_hidden_size 256 \
    --dropout_rate 0.2 \
    --primary_dataset data/dataset_with_hal.jsonl
```

## Evaluation Metrics

The framework provides comprehensive metrics:

- **Token-level**: Precision, Recall, F1 for individual tokens
- **Span-level**: Exact and partial span matching metrics
- **Example-level**: Whether entire examples are correctly classified

## Results Analysis

After evaluation, you'll get:

1. **Comprehensive Report**: JSON file with all metrics
2. **Predictions**: Detailed predictions for error analysis
3. **Statistical Tests**: Significance testing between models
4. **Error Analysis**: Breakdown of different error types

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Citation

If you use this framework in your research, please cite:

```bibtex
@software{modernbert_hallucination_detection,
  title={ModernBERT Hallucination Detection Framework},
  author={Your Name},
  year={2024},
  url={https://github.com/your-username/modernbert-hallucination-detection}
}
```

## Acknowledgments

- [ModernBERT](https://huggingface.co/answerdotai/ModernBERT-base) by Answer.AI
- [RAGTruth Dataset](https://github.com/ParticleMedia/RAGTruth) by Particle Media
- [LettuceDetect](https://github.com/KRLabsOrg/LettuceDetect) for inspiration and reference implementation
