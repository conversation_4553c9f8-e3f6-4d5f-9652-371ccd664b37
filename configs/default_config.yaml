# Default configuration for ModernBERT hallucination detection

# Data configuration
data:
  primary_dataset: "data/dataset_with_hal.jsonl"
  ragtruth_dataset: "data/ragtruth/ragtruth_converted.jsonl"
  use_ragtruth: false
  max_length: 512
  train_ratio: 0.8
  val_ratio: 0.1
  random_seed: 42

# Model configuration
model:
  name: "answerdotai/ModernBERT-base"
  num_labels: 2
  dropout_rate: 0.1
  classifier_hidden_size: null  # null for single layer
  use_crf: false

# Training configuration
training:
  num_epochs: 3
  batch_size: 16
  learning_rate: 2.0e-5
  weight_decay: 0.01
  warmup_steps: 0
  max_grad_norm: 1.0
  
  # Logging and saving
  logging_steps: 100
  save_steps: 500
  eval_steps: 500
  
  # Weights & Biases
  use_wandb: false
  wandb_project: "hallucination-detection"

# Evaluation configuration
evaluation:
  batch_size: 32
  compute_spans: true
  save_predictions: true
  statistical_tests: true
  error_analysis: true

# Output configuration
output:
  base_dir: "outputs"
  log_level: "INFO"
