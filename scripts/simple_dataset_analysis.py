#!/usr/bin/env python3
"""
Simple Dataset Analysis Script for Hallucination Detection

This script analyzes the structure and content of the dataset_with_hal.jsonl file
using only standard library modules.
"""

import json
from collections import Counter, defaultdict
from pathlib import Path
import re

def load_jsonl(file_path):
    """Load JSONL file and return list of dictionaries."""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line.strip()))
    return data

def analyze_dataset_structure(data):
    """Analyze the basic structure of the dataset."""
    print("=== Dataset Structure Analysis ===")
    print(f"Total samples: {len(data)}")
    
    # Check fields
    if data:
        sample = data[0]
        print(f"Fields in each sample: {list(sample.keys())}")
        
        # Analyze each field
        for field in sample.keys():
            print(f"\n{field}:")
            if field == 'context':
                contexts = [len(item[field]) for item in data]
                print(f"  - Number of context passages per sample: min={min(contexts)}, max={max(contexts)}, avg={sum(contexts)/len(contexts):.1f}")
            elif field == 'hal':
                hal_values = [item[field] for item in data]
                hal_counter = Counter(hal_values)
                print(f"  - Hallucination labels: {dict(hal_counter)}")
            elif field == 'hal_span':
                non_empty_spans = [item[field] for item in data if item[field]]
                print(f"  - Non-empty hallucination spans: {len(non_empty_spans)}/{len(data)}")
    
    return data

def analyze_hallucination_patterns(data):
    """Analyze hallucination patterns in the dataset."""
    print("\n=== Hallucination Pattern Analysis ===")
    
    # Basic statistics
    hal_labels = [item['hal'] for item in data]
    hal_counter = Counter(hal_labels)
    
    print(f"Hallucination distribution:")
    for label, count in hal_counter.items():
        percentage = (count / len(data)) * 100
        print(f"  - Label {label}: {count} samples ({percentage:.1f}%)")
    
    # Analyze hallucination spans
    hallucinated_samples = [item for item in data if item['hal'] == 1]
    print(f"\nHallucinated samples analysis:")
    print(f"  - Total hallucinated samples: {len(hallucinated_samples)}")
    
    if hallucinated_samples:
        # Analyze span patterns
        span_patterns = []
        for item in hallucinated_samples:
            span = item['hal_span']
            if span:
                # Count <HAL> tags
                hal_tags = span.count('<HAL>')
                span_patterns.append(hal_tags)
        
        if span_patterns:
            print(f"  - Samples with span annotations: {len(span_patterns)}")
            print(f"  - HAL tags per sample: min={min(span_patterns)}, max={max(span_patterns)}, avg={sum(span_patterns)/len(span_patterns):.1f}")

def analyze_text_lengths(data):
    """Analyze text lengths in the dataset."""
    print("\n=== Text Length Analysis ===")
    
    # Context lengths
    context_lengths = []
    for item in data:
        total_context_length = sum(len(ctx) for ctx in item['context'])
        context_lengths.append(total_context_length)
    
    # Question lengths
    question_lengths = [len(item['question']) for item in data]
    
    # Answer lengths
    answer_lengths = [len(item['answer']) for item in data]
    
    print(f"Context lengths: min={min(context_lengths)}, max={max(context_lengths)}, avg={sum(context_lengths)/len(context_lengths):.0f}")
    print(f"Question lengths: min={min(question_lengths)}, max={max(question_lengths)}, avg={sum(question_lengths)/len(question_lengths):.0f}")
    print(f"Answer lengths: min={min(answer_lengths)}, max={max(answer_lengths)}, avg={sum(answer_lengths)/len(answer_lengths):.0f}")

def extract_hallucination_spans(hal_span_text):
    """Extract hallucination spans from the hal_span field."""
    if not hal_span_text:
        return []
    
    # Find all <HAL>...</HAL> patterns
    pattern = r'<HAL>(.*?)</HAL>'
    matches = re.findall(pattern, hal_span_text)
    return matches

def analyze_hallucination_types(data):
    """Analyze types of hallucinations found in the dataset."""
    print("\n=== Hallucination Type Analysis ===")
    
    hallucinated_samples = [item for item in data if item['hal'] == 1]
    
    all_hallucinations = []
    for item in hallucinated_samples:
        spans = extract_hallucination_spans(item['hal_span'])
        all_hallucinations.extend(spans)
    
    print(f"Total hallucination spans extracted: {len(all_hallucinations)}")
    
    if all_hallucinations:
        # Analyze hallucination lengths
        hal_lengths = [len(span) for span in all_hallucinations]
        print(f"Hallucination span lengths: min={min(hal_lengths)}, max={max(hal_lengths)}, avg={sum(hal_lengths)/len(hal_lengths):.1f}")
        
        # Show some examples
        print(f"\nExample hallucination spans:")
        for i, span in enumerate(all_hallucinations[:5]):
            print(f"  {i+1}. '{span}'")

def show_sample_examples(data):
    """Show some sample examples from the dataset."""
    print("\n=== Sample Examples ===")
    
    # Show one non-hallucinated example
    non_hal_samples = [item for item in data if item['hal'] == 0]
    if non_hal_samples:
        sample = non_hal_samples[0]
        print("Non-hallucinated example:")
        print(f"  Question: {sample['question'][:100]}...")
        print(f"  Answer: {sample['answer'][:100]}...")
        print(f"  Hallucination: {sample['hal']}")
        print()
    
    # Show one hallucinated example
    hal_samples = [item for item in data if item['hal'] == 1]
    if hal_samples:
        sample = hal_samples[0]
        print("Hallucinated example:")
        print(f"  Question: {sample['question'][:100]}...")
        print(f"  Answer: {sample['answer'][:100]}...")
        print(f"  Hallucination: {sample['hal']}")
        print(f"  Hallucination span: {sample['hal_span'][:100]}...")

def main():
    """Main analysis function."""
    # Load dataset
    data_path = Path("data/dataset_with_hal.jsonl")
    if not data_path.exists():
        print(f"Error: Dataset file not found at {data_path}")
        return
    
    print("Loading dataset...")
    data = load_jsonl(data_path)
    
    # Run analyses
    analyze_dataset_structure(data)
    analyze_hallucination_patterns(data)
    analyze_text_lengths(data)
    analyze_hallucination_types(data)
    show_sample_examples(data)
    
    print("\n=== Analysis Complete ===")

if __name__ == "__main__":
    main()
