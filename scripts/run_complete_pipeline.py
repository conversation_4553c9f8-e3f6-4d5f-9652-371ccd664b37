#!/usr/bin/env python3
"""
Complete pipeline script for ModernBERT hallucination detection.

This script runs the complete pipeline:
1. Dataset analysis
2. RAGTruth download (optional)
3. Model training on primary dataset
4. Model training with RAGTruth (optional)
5. Comprehensive evaluation and comparison
"""

import argparse
import logging
import subprocess
import sys
from pathlib import Path
import json

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run complete hallucination detection pipeline")
    
    # Pipeline control
    parser.add_argument("--skip_analysis", action="store_true",
                       help="Skip dataset analysis")
    parser.add_argument("--skip_ragtruth", action="store_true",
                       help="Skip RAGTruth download and training")
    parser.add_argument("--skip_training", action="store_true",
                       help="Skip training (use existing models)")
    parser.add_argument("--skip_evaluation", action="store_true",
                       help="Skip evaluation")
    
    # Data arguments
    parser.add_argument("--primary_dataset", type=str, default="data/dataset_with_hal.jsonl",
                       help="Path to primary dataset")
    
    # Training arguments
    parser.add_argument("--num_epochs", type=int, default=3,
                       help="Number of training epochs")
    parser.add_argument("--batch_size", type=int, default=16,
                       help="Training batch size")
    parser.add_argument("--learning_rate", type=float, default=2e-5,
                       help="Learning rate")
    
    # Output arguments
    parser.add_argument("--output_dir", type=str, default="pipeline_outputs",
                       help="Base output directory")
    
    # Logging
    parser.add_argument("--log_level", type=str, default="INFO",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Logging level")
    
    return parser.parse_args()

def setup_logging(level):
    """Setup logging."""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

def run_command(command, description):
    """Run a command and handle errors."""
    logger = logging.getLogger(__name__)
    
    logger.info(f"Running: {description}")
    logger.info(f"Command: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        logger.info(f"✓ {description} completed successfully")
        return result
    except subprocess.CalledProcessError as e:
        logger.error(f"✗ {description} failed")
        logger.error(f"Error: {e}")
        logger.error(f"Stdout: {e.stdout}")
        logger.error(f"Stderr: {e.stderr}")
        raise

def main():
    """Main pipeline function."""
    args = parse_args()
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting complete hallucination detection pipeline")
    logger.info(f"Arguments: {vars(args)}")
    
    # Create output directories
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    primary_model_dir = output_dir / "primary_model"
    ragtruth_model_dir = output_dir / "ragtruth_model"
    evaluation_dir = output_dir / "evaluation"
    
    # Save pipeline configuration
    with open(output_dir / "pipeline_config.json", 'w') as f:
        json.dump(vars(args), f, indent=2)
    
    try:
        # Step 1: Dataset Analysis
        if not args.skip_analysis:
            logger.info("=" * 50)
            logger.info("STEP 1: Dataset Analysis")
            logger.info("=" * 50)
            
            run_command([
                sys.executable, "scripts/simple_dataset_analysis.py"
            ], "Dataset analysis")
        
        # Step 2: RAGTruth Download
        if not args.skip_ragtruth:
            logger.info("=" * 50)
            logger.info("STEP 2: RAGTruth Download")
            logger.info("=" * 50)
            
            run_command([
                sys.executable, "scripts/download_ragtruth.py"
            ], "RAGTruth dataset download")
        
        # Step 3: Training
        if not args.skip_training:
            logger.info("=" * 50)
            logger.info("STEP 3: Model Training")
            logger.info("=" * 50)
            
            # Train on primary dataset only
            logger.info("Training model on primary dataset only...")
            run_command([
                sys.executable, "scripts/train_model.py",
                "--primary_dataset", args.primary_dataset,
                "--output_dir", str(primary_model_dir),
                "--num_epochs", str(args.num_epochs),
                "--batch_size", str(args.batch_size),
                "--learning_rate", str(args.learning_rate),
                "--log_level", args.log_level
            ], "Primary dataset training")
            
            # Train with RAGTruth if not skipped
            if not args.skip_ragtruth:
                logger.info("Training model with RAGTruth dataset...")
                run_command([
                    sys.executable, "scripts/train_model.py",
                    "--primary_dataset", args.primary_dataset,
                    "--use_ragtruth",
                    "--output_dir", str(ragtruth_model_dir),
                    "--num_epochs", str(args.num_epochs),
                    "--batch_size", str(args.batch_size),
                    "--learning_rate", str(args.learning_rate),
                    "--log_level", args.log_level
                ], "RAGTruth combined training")
        
        # Step 4: Evaluation
        if not args.skip_evaluation:
            logger.info("=" * 50)
            logger.info("STEP 4: Model Evaluation")
            logger.info("=" * 50)
            
            # Check which models exist
            primary_best_model = primary_model_dir / "best_model"
            ragtruth_best_model = ragtruth_model_dir / "best_model"
            
            if primary_best_model.exists():
                if ragtruth_best_model.exists() and not args.skip_ragtruth:
                    # Compare both models
                    logger.info("Comparing primary and RAGTruth models...")
                    eval_command = [
                        sys.executable, "scripts/evaluate_model.py",
                        "--model_dir", str(primary_best_model),
                        "--comparison_model_dir", str(ragtruth_best_model),
                        "--primary_dataset", args.primary_dataset,
                        "--output_dir", str(evaluation_dir),
                        "--statistical_tests",
                        "--error_analysis",
                        "--save_predictions",
                        "--log_level", args.log_level
                    ]
                    
                    if not args.skip_ragtruth:
                        eval_command.extend(["--evaluate_ragtruth"])
                    
                    run_command(eval_command, "Model comparison evaluation")
                else:
                    # Evaluate primary model only
                    logger.info("Evaluating primary model...")
                    eval_command = [
                        sys.executable, "scripts/evaluate_model.py",
                        "--model_dir", str(primary_best_model),
                        "--primary_dataset", args.primary_dataset,
                        "--output_dir", str(evaluation_dir),
                        "--statistical_tests",
                        "--error_analysis",
                        "--save_predictions",
                        "--log_level", args.log_level
                    ]
                    
                    if not args.skip_ragtruth:
                        eval_command.extend(["--evaluate_ragtruth"])
                    
                    run_command(eval_command, "Primary model evaluation")
            else:
                logger.warning("No trained models found for evaluation")
        
        # Step 5: Results Summary
        logger.info("=" * 50)
        logger.info("PIPELINE COMPLETED SUCCESSFULLY")
        logger.info("=" * 50)
        
        # Print summary of outputs
        logger.info("Output directories:")
        if primary_model_dir.exists():
            logger.info(f"  Primary model: {primary_model_dir}")
        if ragtruth_model_dir.exists():
            logger.info(f"  RAGTruth model: {ragtruth_model_dir}")
        if evaluation_dir.exists():
            logger.info(f"  Evaluation results: {evaluation_dir}")
        
        # Print key results if available
        comprehensive_report = evaluation_dir / "comprehensive_report.json"
        if comprehensive_report.exists():
            logger.info("\nKey Results:")
            with open(comprehensive_report, 'r') as f:
                report = json.load(f)
            
            if 'summary' in report and 'key_metrics' in report['summary']:
                for metric, data in report['summary']['key_metrics'].items():
                    logger.info(f"  {metric.upper()}:")
                    for dataset, value in data['by_dataset'].items():
                        logger.info(f"    {dataset}: {value:.4f}")
                    logger.info(f"    Average: {data['average']:.4f}")
        
        logger.info(f"\nAll outputs saved to: {output_dir}")
        logger.info("Pipeline completed successfully! 🎉")
        
    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
