#!/usr/bin/env python3
"""
Test script to verify the setup is working correctly.

This script performs basic tests to ensure all components are properly installed
and configured.
"""

import sys
from pathlib import Path
import logging

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
    except ImportError as e:
        print(f"✗ PyTorch import failed: {e}")
        return False
    
    try:
        import transformers
        print(f"✓ Transformers {transformers.__version__}")
    except ImportError as e:
        print(f"✗ Transformers import failed: {e}")
        return False
    
    try:
        from hallucination_detection.models.modernbert_classifier import create_model_and_tokenizer
        print("✓ ModernBERT classifier")
    except ImportError as e:
        print(f"✗ ModernBERT classifier import failed: {e}")
        return False
    
    try:
        from hallucination_detection.data.dataset_loader import load_dataset_with_hal
        print("✓ Dataset loader")
    except ImportError as e:
        print(f"✗ Dataset loader import failed: {e}")
        return False
    
    try:
        from hallucination_detection.training.trainer import HallucinationTrainer
        print("✓ Trainer")
    except ImportError as e:
        print(f"✗ Trainer import failed: {e}")
        return False
    
    try:
        from hallucination_detection.evaluation.evaluator import HallucinationEvaluator
        print("✓ Evaluator")
    except ImportError as e:
        print(f"✗ Evaluator import failed: {e}")
        return False
    
    return True

def test_dataset():
    """Test that the dataset can be loaded."""
    print("\nTesting dataset loading...")
    
    dataset_path = Path("data/dataset_with_hal.jsonl")
    if not dataset_path.exists():
        print(f"✗ Dataset not found at {dataset_path}")
        return False
    
    try:
        from hallucination_detection.data.dataset_loader import load_dataset_with_hal
        data = load_dataset_with_hal(str(dataset_path))
        print(f"✓ Dataset loaded: {len(data)} samples")
        
        # Check first sample structure
        if data:
            sample = data[0]
            required_fields = ['context', 'question', 'answer', 'hal', 'hal_span']
            for field in required_fields:
                if field not in sample:
                    print(f"✗ Missing field '{field}' in dataset")
                    return False
            print("✓ Dataset structure is correct")
        
        return True
    except Exception as e:
        print(f"✗ Dataset loading failed: {e}")
        return False

def test_model_creation():
    """Test that the model can be created."""
    print("\nTesting model creation...")
    
    try:
        from hallucination_detection.models.modernbert_classifier import create_model_and_tokenizer
        
        # Create a small model for testing
        model, tokenizer = create_model_and_tokenizer(
            model_name="answerdotai/ModernBERT-base",
            num_labels=2
        )
        
        print("✓ Model and tokenizer created successfully")
        
        # Test tokenization
        test_text = "This is a test sentence."
        tokens = tokenizer(test_text, return_tensors="pt")
        print(f"✓ Tokenization works: {tokens['input_ids'].shape}")
        
        # Test model forward pass
        import torch
        with torch.no_grad():
            outputs = model(**tokens)
        
        print(f"✓ Model forward pass works: {outputs['logits'].shape}")
        
        return True
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        return False

def test_preprocessing():
    """Test data preprocessing."""
    print("\nTesting data preprocessing...")
    
    try:
        from hallucination_detection.models.modernbert_classifier import create_model_and_tokenizer
        from hallucination_detection.data.preprocessing import preprocess_sample
        
        # Create model and tokenizer
        model, tokenizer = create_model_and_tokenizer()
        
        # Test sample
        sample = {
            'context': ['This is context.'],
            'question': 'What is this?',
            'answer': 'This is a test.',
            'hal': 0,
            'hal_span': ''
        }
        
        processed = preprocess_sample(sample, tokenizer)
        
        if processed is None:
            print("✗ Preprocessing returned None")
            return False
        
        required_keys = ['input_ids', 'attention_mask', 'labels']
        for key in required_keys:
            if key not in processed:
                print(f"✗ Missing key '{key}' in processed sample")
                return False
        
        print("✓ Data preprocessing works")
        return True
    except Exception as e:
        print(f"✗ Data preprocessing failed: {e}")
        return False

def test_device():
    """Test device availability."""
    print("\nTesting device availability...")
    
    try:
        import torch
        
        if torch.cuda.is_available():
            print(f"✓ CUDA available: {torch.cuda.get_device_name()}")
            print(f"  CUDA version: {torch.version.cuda}")
            print(f"  GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        elif torch.backends.mps.is_available():
            print("✓ MPS (Apple Silicon) available")
        else:
            print("ℹ Using CPU (CUDA/MPS not available)")
        
        return True
    except Exception as e:
        print(f"✗ Device test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("ModernBERT Hallucination Detection Setup Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Dataset Test", test_dataset),
        ("Model Creation Test", test_model_creation),
        ("Preprocessing Test", test_preprocessing),
        ("Device Test", test_device)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Run dataset analysis: python scripts/simple_dataset_analysis.py")
        print("2. Download RAGTruth (optional): python scripts/download_ragtruth.py")
        print("3. Train a model: python scripts/train_model.py")
        print("4. Or run the complete pipeline: python scripts/run_complete_pipeline.py")
    else:
        print(f"\n❌ {len(results) - passed} tests failed. Please fix the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
