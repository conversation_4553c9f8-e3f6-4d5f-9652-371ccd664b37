#!/usr/bin/env python3
"""
Script to download and preprocess the RAGTruth dataset.

This script downloads the RAGTruth dataset from the official repository
and converts it to our format for comparison.
"""

import json
import requests
from pathlib import Path
import logging
from typing import List, Dict
import zipfile
import tempfile

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# RAGTruth dataset URLs
RAGTRUTH_URLS = {
    'response': 'https://github.com/ParticleMedia/RAGTruth/raw/main/dataset/response.jsonl',
    'source_info': 'https://github.com/ParticleMedia/RAGTruth/raw/main/dataset/source_info.jsonl'
}

def download_file(url: str, output_path: Path) -> bool:
    """
    Download a file from URL to output path.
    
    Args:
        url: URL to download from
        output_path: Path to save the file
        
    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Downloading {url}")
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        logger.info(f"Downloaded to {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error downloading {url}: {e}")
        return False

def load_jsonl(file_path: Path) -> List[Dict]:
    """Load JSONL file."""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line.strip()))
    return data

def convert_ragtruth_to_our_format(responses: List[Dict], source_infos: List[Dict]) -> List[Dict]:
    """
    Convert RAGTruth format to our dataset format.
    
    Args:
        responses: List of response dictionaries from RAGTruth
        source_infos: List of source info dictionaries from RAGTruth
        
    Returns:
        List of samples in our format
    """
    # Create a mapping from source_id to source_info
    source_map = {info['source_id']: info for info in source_infos}
    
    converted_samples = []
    
    for response in responses:
        source_id = response['source_id']
        if source_id not in source_map:
            continue
        
        source_info = source_map[source_id]
        
        # Extract context based on task type
        if source_info['task_type'] == 'QA':
            # For QA tasks, context comes from passages
            context_data = source_info['source_info']
            if isinstance(context_data, dict) and 'passages' in context_data:
                # Split passages into individual context items
                passages_text = context_data['passages']
                # Simple split by "passage N:" pattern
                import re
                passages = re.split(r'passage \d+:', passages_text)
                context = [p.strip() for p in passages if p.strip()]
                question = context_data.get('question', '')
            else:
                continue
                
        elif source_info['task_type'] == 'Summary':
            # For summarization, the source_info is the context
            context = [source_info['source_info']]
            question = "Summarize the following text:"
            
        elif source_info['task_type'] == 'Data2txt':
            # For data-to-text, convert structured data to context
            context_data = source_info['source_info']
            if isinstance(context_data, dict):
                # Convert dict to readable text
                context_text = json.dumps(context_data, indent=2)
                context = [context_text]
                question = "Write an overview based on the following data:"
            else:
                continue
        else:
            continue
        
        # Extract hallucination information
        hal_label = 1 if response['labels'] else 0
        hal_span = ""
        
        if response['labels']:
            # Convert RAGTruth labels to our format
            answer_text = response['response']
            hal_spans = []
            
            for label in response['labels']:
                start = label['start']
                end = label['end']
                hal_text = label['text']
                
                # Create our format with <HAL> tags
                hal_spans.append((start, end, hal_text))
            
            # Sort by start position
            hal_spans.sort(key=lambda x: x[0])
            
            # Build hal_span string with <HAL> tags
            hal_span = answer_text
            offset = 0
            for start, end, hal_text in hal_spans:
                # Insert <HAL> tags
                adjusted_start = start + offset
                adjusted_end = end + offset
                
                hal_span = (hal_span[:adjusted_start] + 
                           f"<HAL>{hal_text}</HAL>" + 
                           hal_span[adjusted_end:])
                
                # Update offset for next insertion
                offset += len("<HAL></HAL>")
        
        # Create sample in our format
        sample = {
            'context': context,
            'question': question,
            'answer': response['response'],
            'hal': hal_label,
            'hal_span': hal_span
        }
        
        converted_samples.append(sample)
    
    logger.info(f"Converted {len(converted_samples)} RAGTruth samples")
    return converted_samples

def download_and_convert_ragtruth(output_dir: str = "data/ragtruth"):
    """
    Download and convert RAGTruth dataset.
    
    Args:
        output_dir: Directory to save the converted dataset
    """
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Download files
    response_path = output_path / "response.jsonl"
    source_info_path = output_path / "source_info.jsonl"
    
    success = True
    if not response_path.exists():
        success &= download_file(RAGTRUTH_URLS['response'], response_path)
    
    if not source_info_path.exists():
        success &= download_file(RAGTRUTH_URLS['source_info'], source_info_path)
    
    if not success:
        logger.error("Failed to download RAGTruth dataset")
        return
    
    # Load data
    logger.info("Loading RAGTruth data...")
    responses = load_jsonl(response_path)
    source_infos = load_jsonl(source_info_path)
    
    logger.info(f"Loaded {len(responses)} responses and {len(source_infos)} source infos")
    
    # Convert to our format
    logger.info("Converting to our format...")
    converted_samples = convert_ragtruth_to_our_format(responses, source_infos)
    
    # Save converted dataset
    output_file = output_path / "ragtruth_converted.jsonl"
    with open(output_file, 'w', encoding='utf-8') as f:
        for sample in converted_samples:
            f.write(json.dumps(sample) + '\n')
    
    logger.info(f"Saved converted RAGTruth dataset to {output_file}")
    
    # Create a summary
    hal_count = sum(1 for s in converted_samples if s['hal'] == 1)
    logger.info(f"RAGTruth dataset summary:")
    logger.info(f"  Total samples: {len(converted_samples)}")
    logger.info(f"  Hallucinated samples: {hal_count} ({hal_count/len(converted_samples)*100:.1f}%)")
    logger.info(f"  Non-hallucinated samples: {len(converted_samples) - hal_count}")

def main():
    """Main function."""
    download_and_convert_ragtruth()

if __name__ == "__main__":
    main()
