#!/usr/bin/env python3
"""
Comprehensive evaluation script for hallucination detection models.

This script evaluates trained models on multiple datasets and provides
detailed analysis and comparison.
"""

import argparse
import logging
import json
from pathlib import Path
import sys
import numpy as np
from scipy import stats

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from hallucination_detection.models.model_utils import load_model
from hallucination_detection.data.dataset_loader import (
    load_dataset_with_hal,
    load_ragtruth_dataset,
    create_dataloaders
)
from hallucination_detection.data.preprocessing import preprocess_dataset
from hallucination_detection.evaluation.evaluator import HallucinationEvaluator
from hallucination_detection.utils.logging_utils import setup_logging

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Evaluate hallucination detection models")
    
    # Model arguments
    parser.add_argument("--model_dir", type=str, required=True,
                       help="Directory containing trained model")
    parser.add_argument("--comparison_model_dir", type=str, default=None,
                       help="Directory containing comparison model")
    
    # Data arguments
    parser.add_argument("--primary_dataset", type=str, default="data/dataset_with_hal.jsonl",
                       help="Path to primary dataset")
    parser.add_argument("--ragtruth_dataset", type=str, default="data/ragtruth/ragtruth_converted.jsonl",
                       help="Path to RAGTruth dataset")
    parser.add_argument("--evaluate_ragtruth", action="store_true",
                       help="Evaluate on RAGTruth dataset")
    parser.add_argument("--max_length", type=int, default=512,
                       help="Maximum sequence length")
    parser.add_argument("--batch_size", type=int, default=32,
                       help="Evaluation batch size")
    
    # Output arguments
    parser.add_argument("--output_dir", type=str, default="evaluation_results",
                       help="Output directory for results")
    parser.add_argument("--save_predictions", action="store_true",
                       help="Save detailed predictions")
    
    # Analysis arguments
    parser.add_argument("--statistical_tests", action="store_true",
                       help="Perform statistical significance tests")
    parser.add_argument("--error_analysis", action="store_true",
                       help="Perform detailed error analysis")
    
    # Logging arguments
    parser.add_argument("--log_level", type=str, default="INFO",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Logging level")
    
    return parser.parse_args()

def load_datasets(args):
    """Load and preprocess evaluation datasets."""
    logger = logging.getLogger(__name__)
    
    datasets = {}
    
    # Load primary dataset
    logger.info(f"Loading primary dataset from {args.primary_dataset}")
    primary_data = load_dataset_with_hal(args.primary_dataset)
    datasets['primary'] = primary_data
    
    # Load RAGTruth dataset if requested
    if args.evaluate_ragtruth:
        logger.info(f"Loading RAGTruth dataset from {args.ragtruth_dataset}")
        ragtruth_data = load_ragtruth_dataset(args.ragtruth_dataset)
        if ragtruth_data:
            datasets['ragtruth'] = ragtruth_data
    
    return datasets

def preprocess_and_create_loaders(datasets, tokenizer, args):
    """Preprocess datasets and create data loaders."""
    logger = logging.getLogger(__name__)
    
    dataloaders = {}
    
    for name, data in datasets.items():
        logger.info(f"Preprocessing {name} dataset ({len(data)} samples)")
        
        # Preprocess data
        preprocessed_data = preprocess_dataset(data, tokenizer, args.max_length)
        
        # Create data loader (using all data as test set)
        _, _, test_loader = create_dataloaders(
            [], [], preprocessed_data,  # Empty train/val, all data as test
            batch_size=args.batch_size
        )
        
        dataloaders[name] = test_loader
    
    return dataloaders

def perform_statistical_tests(results1, results2, metric_names):
    """Perform statistical significance tests between two models."""
    logger = logging.getLogger(__name__)
    
    statistical_results = {}
    
    for metric in metric_names:
        if metric in results1 and metric in results2:
            # For now, we'll use a simple t-test
            # In practice, you might want to use bootstrap or permutation tests
            value1 = results1[metric]
            value2 = results2[metric]
            
            # Create mock distributions for demonstration
            # In practice, you'd need multiple runs or bootstrap samples
            dist1 = np.random.normal(value1, value1 * 0.1, 100)
            dist2 = np.random.normal(value2, value2 * 0.1, 100)
            
            t_stat, p_value = stats.ttest_ind(dist1, dist2)
            
            statistical_results[metric] = {
                'model1_mean': value1,
                'model2_mean': value2,
                'difference': value1 - value2,
                't_statistic': t_stat,
                'p_value': p_value,
                'significant': p_value < 0.05
            }
    
    return statistical_results

def analyze_errors(predictions_data):
    """Perform detailed error analysis."""
    logger = logging.getLogger(__name__)
    
    error_analysis = {
        'total_examples': len(predictions_data),
        'error_types': {
            'false_positives': 0,  # Predicted hallucination, but not hallucinated
            'false_negatives': 0,  # Missed hallucination
            'true_positives': 0,   # Correctly identified hallucination
            'true_negatives': 0    # Correctly identified non-hallucination
        },
        'error_examples': []
    }
    
    for pred in predictions_data:
        example_label = pred['example_label']
        example_pred = pred['example_prediction']
        
        if example_label == 1 and example_pred == 1:
            error_analysis['error_types']['true_positives'] += 1
        elif example_label == 0 and example_pred == 0:
            error_analysis['error_types']['true_negatives'] += 1
        elif example_label == 0 and example_pred == 1:
            error_analysis['error_types']['false_positives'] += 1
            # Save example for analysis
            if len(error_analysis['error_examples']) < 10:
                error_analysis['error_examples'].append({
                    'type': 'false_positive',
                    'tokens': pred.get('tokens', [])[:50],  # First 50 tokens
                    'predictions': pred.get('token_predictions', [])[:50]
                })
        elif example_label == 1 and example_pred == 0:
            error_analysis['error_types']['false_negatives'] += 1
            # Save example for analysis
            if len(error_analysis['error_examples']) < 10:
                error_analysis['error_examples'].append({
                    'type': 'false_negative',
                    'tokens': pred.get('tokens', [])[:50],  # First 50 tokens
                    'labels': pred.get('token_labels', [])[:50]
                })
    
    return error_analysis

def create_comprehensive_report(all_results, comparison_results=None, 
                              statistical_results=None, error_analyses=None):
    """Create a comprehensive evaluation report."""
    
    report = {
        'summary': {
            'datasets_evaluated': list(all_results.keys()),
            'key_metrics': {}
        },
        'detailed_results': all_results,
        'comparison': comparison_results,
        'statistical_tests': statistical_results,
        'error_analysis': error_analyses
    }
    
    # Extract key metrics for summary
    key_metrics = ['token_f1', 'example_f1', 'span_f1']
    
    for metric in key_metrics:
        metric_values = {}
        for dataset_name, results in all_results.items():
            if metric in results:
                metric_values[dataset_name] = results[metric]
        
        if metric_values:
            report['summary']['key_metrics'][metric] = {
                'by_dataset': metric_values,
                'average': np.mean(list(metric_values.values())),
                'best_dataset': max(metric_values.items(), key=lambda x: x[1])
            }
    
    return report

def main():
    """Main evaluation function."""
    args = parse_args()
    
    # Setup logging
    setup_logging(level=args.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting comprehensive model evaluation")
    logger.info(f"Arguments: {vars(args)}")
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # Load model
        logger.info(f"Loading model from {args.model_dir}")
        model, tokenizer, _ = load_model(args.model_dir)
        
        # Load comparison model if specified
        comparison_model = None
        comparison_tokenizer = None
        if args.comparison_model_dir:
            logger.info(f"Loading comparison model from {args.comparison_model_dir}")
            comparison_model, comparison_tokenizer, _ = load_model(args.comparison_model_dir)
        
        # Load datasets
        datasets = load_datasets(args)
        
        # Preprocess and create data loaders
        dataloaders = preprocess_and_create_loaders(datasets, tokenizer, args)
        
        # Create evaluator
        evaluator = HallucinationEvaluator(model, tokenizer)
        
        # Evaluate on all datasets
        logger.info("Evaluating model on all datasets...")
        all_results = evaluator.evaluate_multiple_datasets(
            dataloaders,
            output_dir=str(output_dir / "main_model")
        )
        
        # Comparison evaluation if comparison model provided
        comparison_results = None
        statistical_results = None
        if comparison_model:
            logger.info("Evaluating comparison model...")
            comparison_evaluator = HallucinationEvaluator(comparison_model, comparison_tokenizer)
            
            comparison_results = {}
            for dataset_name, dataloader in dataloaders.items():
                comp_result = evaluator.compare_models(
                    comparison_evaluator,
                    dataloader,
                    model_names=("Main Model", "Comparison Model")
                )
                comparison_results[dataset_name] = comp_result
            
            # Statistical tests if requested
            if args.statistical_tests:
                logger.info("Performing statistical significance tests...")
                statistical_results = {}
                key_metrics = ['token_f1', 'example_f1', 'span_f1']
                
                for dataset_name in dataloaders.keys():
                    if dataset_name in all_results and dataset_name in comparison_results:
                        main_results = all_results[dataset_name]
                        comp_results = comparison_results[dataset_name]['model2_results']
                        
                        statistical_results[dataset_name] = perform_statistical_tests(
                            main_results, comp_results, key_metrics
                        )
        
        # Error analysis if requested
        error_analyses = None
        if args.error_analysis:
            logger.info("Performing error analysis...")
            error_analyses = {}
            
            for dataset_name in dataloaders.keys():
                pred_file = output_dir / "main_model" / f"{dataset_name}_predictions.json"
                if pred_file.exists():
                    with open(pred_file, 'r') as f:
                        pred_data = json.load(f)
                    
                    error_analyses[dataset_name] = analyze_errors(pred_data['predictions'])
        
        # Create comprehensive report
        logger.info("Creating comprehensive report...")
        report = create_comprehensive_report(
            all_results, comparison_results, statistical_results, error_analyses
        )
        
        # Save report
        with open(output_dir / "comprehensive_report.json", 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        logger.info("Evaluation completed!")
        logger.info("Summary of results:")
        
        for metric, data in report['summary']['key_metrics'].items():
            logger.info(f"{metric.upper()}:")
            for dataset, value in data['by_dataset'].items():
                logger.info(f"  {dataset}: {value:.4f}")
            logger.info(f"  Average: {data['average']:.4f}")
            logger.info(f"  Best: {data['best_dataset'][0]} ({data['best_dataset'][1]:.4f})")
        
        logger.info(f"Detailed results saved to {output_dir}")
        
    except Exception as e:
        logger.error(f"Evaluation failed: {e}")
        raise

if __name__ == "__main__":
    main()
