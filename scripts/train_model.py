#!/usr/bin/env python3
"""
Main training script for ModernBERT hallucination detection.

This script trains a ModernBERT model for token-level hallucination detection
on the provided dataset and optionally compares with RAGTruth.
"""

import argparse
import logging
import json
from pathlib import Path
import sys
import os

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from hallucination_detection.models.modernbert_classifier import create_model_and_tokenizer
from hallucination_detection.data.dataset_loader import (
    load_dataset_with_hal,
    load_ragtruth_dataset,
    split_dataset,
    create_dataloaders
)
from hallucination_detection.data.preprocessing import preprocess_dataset
from hallucination_detection.training.trainer import HallucinationTrainer
from hallucination_detection.evaluation.evaluator import HallucinationEvaluator
from hallucination_detection.utils.logging_utils import setup_logging

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Train ModernBERT for hallucination detection")
    
    # Data arguments
    parser.add_argument("--primary_dataset", type=str, default="data/dataset_with_hal.jsonl",
                       help="Path to primary dataset")
    parser.add_argument("--ragtruth_dataset", type=str, default="data/ragtruth/ragtruth_converted.jsonl",
                       help="Path to RAGTruth dataset")
    parser.add_argument("--use_ragtruth", action="store_true",
                       help="Include RAGTruth dataset in training")
    parser.add_argument("--max_length", type=int, default=512,
                       help="Maximum sequence length")
    
    # Model arguments
    parser.add_argument("--model_name", type=str, default="answerdotai/ModernBERT-base",
                       help="Base model name")
    parser.add_argument("--num_labels", type=int, default=2,
                       help="Number of classification labels")
    parser.add_argument("--dropout_rate", type=float, default=0.1,
                       help="Dropout rate")
    parser.add_argument("--classifier_hidden_size", type=int, default=None,
                       help="Hidden size for classifier (None for single layer)")
    parser.add_argument("--use_crf", action="store_true",
                       help="Use CRF for sequence labeling")
    
    # Training arguments
    parser.add_argument("--num_epochs", type=int, default=3,
                       help="Number of training epochs")
    parser.add_argument("--batch_size", type=int, default=16,
                       help="Training batch size")
    parser.add_argument("--learning_rate", type=float, default=2e-5,
                       help="Learning rate")
    parser.add_argument("--weight_decay", type=float, default=0.01,
                       help="Weight decay")
    parser.add_argument("--warmup_steps", type=int, default=0,
                       help="Number of warmup steps")
    parser.add_argument("--max_grad_norm", type=float, default=1.0,
                       help="Maximum gradient norm for clipping")
    
    # Data split arguments
    parser.add_argument("--train_ratio", type=float, default=0.8,
                       help="Training data ratio")
    parser.add_argument("--val_ratio", type=float, default=0.1,
                       help="Validation data ratio")
    parser.add_argument("--random_seed", type=int, default=42,
                       help="Random seed")
    
    # Output arguments
    parser.add_argument("--output_dir", type=str, default="outputs",
                       help="Output directory")
    parser.add_argument("--logging_steps", type=int, default=100,
                       help="Steps between logging")
    parser.add_argument("--save_steps", type=int, default=500,
                       help="Steps between saving checkpoints")
    parser.add_argument("--eval_steps", type=int, default=500,
                       help="Steps between evaluations")
    
    # Logging arguments
    parser.add_argument("--use_wandb", action="store_true",
                       help="Use Weights & Biases logging")
    parser.add_argument("--wandb_project", type=str, default="hallucination-detection",
                       help="W&B project name")
    parser.add_argument("--log_level", type=str, default="INFO",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Logging level")
    
    return parser.parse_args()

def load_and_preprocess_data(args):
    """Load and preprocess datasets."""
    logger = logging.getLogger(__name__)
    
    # Load primary dataset
    logger.info(f"Loading primary dataset from {args.primary_dataset}")
    primary_data = load_dataset_with_hal(args.primary_dataset)
    
    # Load RAGTruth dataset if requested
    ragtruth_data = []
    if args.use_ragtruth:
        logger.info(f"Loading RAGTruth dataset from {args.ragtruth_dataset}")
        ragtruth_data = load_ragtruth_dataset(args.ragtruth_dataset)
    
    # Combine datasets
    all_data = primary_data + ragtruth_data
    logger.info(f"Total samples: {len(all_data)} (Primary: {len(primary_data)}, RAGTruth: {len(ragtruth_data)})")
    
    # Create model and tokenizer for preprocessing
    model, tokenizer = create_model_and_tokenizer(
        model_name=args.model_name,
        num_labels=args.num_labels,
        dropout_rate=args.dropout_rate,
        classifier_hidden_size=args.classifier_hidden_size,
        use_crf=args.use_crf
    )
    
    # Preprocess data
    logger.info("Preprocessing data...")
    preprocessed_data = preprocess_dataset(all_data, tokenizer, args.max_length)
    
    # Split data
    train_data, val_data, test_data = split_dataset(
        preprocessed_data,
        train_ratio=args.train_ratio,
        val_ratio=args.val_ratio,
        random_seed=args.random_seed
    )
    
    # Create data loaders
    train_loader, val_loader, test_loader = create_dataloaders(
        train_data, val_data, test_data,
        batch_size=args.batch_size
    )
    
    return model, tokenizer, train_loader, val_loader, test_loader

def main():
    """Main training function."""
    args = parse_args()
    
    # Setup logging
    setup_logging(level=args.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("Starting ModernBERT hallucination detection training")
    logger.info(f"Arguments: {vars(args)}")
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save configuration
    with open(output_dir / "config.json", 'w') as f:
        json.dump(vars(args), f, indent=2)
    
    try:
        # Load and preprocess data
        model, tokenizer, train_loader, val_loader, test_loader = load_and_preprocess_data(args)
        
        # Create trainer
        trainer = HallucinationTrainer(
            model=model,
            tokenizer=tokenizer,
            train_dataloader=train_loader,
            val_dataloader=val_loader,
            test_dataloader=test_loader,
            learning_rate=args.learning_rate,
            weight_decay=args.weight_decay,
            warmup_steps=args.warmup_steps,
            max_grad_norm=args.max_grad_norm,
            output_dir=str(output_dir),
            logging_steps=args.logging_steps,
            save_steps=args.save_steps,
            eval_steps=args.eval_steps,
            use_wandb=args.use_wandb,
            wandb_project=args.wandb_project
        )
        
        # Train model
        trainer.train(args.num_epochs)
        
        # Final evaluation
        logger.info("Performing final evaluation...")
        evaluator = HallucinationEvaluator(model, tokenizer)
        
        # Evaluate on test set
        test_results = evaluator.evaluate_dataloader(
            test_loader,
            compute_spans=True,
            save_predictions=True,
            output_file=output_dir / "test_predictions.json"
        )
        
        # Save test results
        with open(output_dir / "test_results.json", 'w') as f:
            json.dump(test_results, f, indent=2)
        
        logger.info("Training completed successfully!")
        logger.info(f"Test F1 Score: {test_results.get('token_f1', 'N/A'):.4f}")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        raise

if __name__ == "__main__":
    main()
