# Getting Started with ModernBERT Hallucination Detection

This guide will help you get started with training and evaluating ModernBERT models for hallucination detection.

## Prerequisites

- Python 3.8 or higher
- CUDA-compatible GPU (recommended) or CPU
- At least 8GB RAM (16GB+ recommended for training)

## Installation

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Verify installation:**
   ```bash
   python scripts/test_setup.py
   ```

## Quick Start (5 minutes)

### Option 1: Complete Pipeline
Run everything with one command:
```bash
python scripts/run_complete_pipeline.py \
    --primary_dataset data/dataset_with_hal.jsonl \
    --num_epochs 1 \
    --batch_size 8
```

### Option 2: Step by Step

1. **Analyze your dataset:**
   ```bash
   python scripts/simple_dataset_analysis.py
   ```

2. **Train a model:**
   ```bash
   python scripts/train_model.py \
       --primary_dataset data/dataset_with_hal.jsonl \
       --output_dir outputs/quick_test \
       --num_epochs 1 \
       --batch_size 8
   ```

3. **Evaluate the model:**
   ```bash
   python scripts/evaluate_model.py \
       --model_dir outputs/quick_test/best_model \
       --primary_dataset data/dataset_with_hal.jsonl \
       --output_dir evaluation_results
   ```

## Understanding Your Dataset

Your dataset analysis will show:
- **Total samples**: 1,500 examples
- **Hallucination distribution**: ~62% hallucinated, ~38% non-hallucinated
- **Text lengths**: Context (~3,349 chars), Questions (~95 chars), Answers (~254 chars)
- **Hallucination patterns**: Various types from factual errors to complete fabrications

## Training Options

### Basic Training
```bash
python scripts/train_model.py \
    --primary_dataset data/dataset_with_hal.jsonl \
    --num_epochs 3 \
    --batch_size 16
```

### With RAGTruth Comparison
```bash
# First download RAGTruth
python scripts/download_ragtruth.py

# Then train with both datasets
python scripts/train_model.py \
    --primary_dataset data/dataset_with_hal.jsonl \
    --use_ragtruth \
    --num_epochs 3 \
    --batch_size 16
```

### Advanced Options
```bash
python scripts/train_model.py \
    --primary_dataset data/dataset_with_hal.jsonl \
    --use_crf \
    --classifier_hidden_size 256 \
    --dropout_rate 0.2 \
    --learning_rate 1e-5 \
    --num_epochs 5 \
    --use_wandb \
    --wandb_project my-hallucination-project
```

## Expected Results

Based on similar datasets, you can expect:

- **Token-level F1**: 0.75-0.85
- **Example-level F1**: 0.80-0.90
- **Span-level F1**: 0.65-0.80

Your dataset has a good balance of hallucinated vs non-hallucinated examples, which should lead to robust model performance.

## Troubleshooting

### Common Issues

1. **Out of Memory Error**
   ```bash
   # Reduce batch size
   python scripts/train_model.py --batch_size 8
   
   # Or reduce max sequence length
   python scripts/train_model.py --max_length 256
   ```

2. **Slow Training**
   ```bash
   # Use fewer epochs for testing
   python scripts/train_model.py --num_epochs 1
   
   # Or smaller dataset subset (modify the script)
   ```

3. **CUDA Not Available**
   - The framework will automatically use CPU
   - Training will be slower but still functional

### Performance Tips

1. **For faster training:**
   - Use GPU if available
   - Increase batch size (if memory allows)
   - Use mixed precision training (add `--fp16` if implemented)

2. **For better results:**
   - Train for more epochs (5-10)
   - Use learning rate scheduling
   - Try different model architectures (CRF, larger classifier)

## Next Steps

1. **Experiment with hyperparameters:**
   - Learning rates: 1e-5, 2e-5, 5e-5
   - Dropout rates: 0.1, 0.2, 0.3
   - Batch sizes: 8, 16, 32

2. **Try different architectures:**
   - Enable CRF for sequence labeling
   - Add hidden layers to classifier
   - Experiment with different base models

3. **Analyze results:**
   - Look at error analysis in evaluation results
   - Identify patterns in misclassified examples
   - Consider data augmentation or additional training data

4. **Compare with baselines:**
   - Train on RAGTruth for comparison
   - Implement simple baseline models
   - Compare with LettuceDetect results

## Getting Help

- Check the logs for detailed error messages
- Run `python scripts/test_setup.py` to verify installation
- Review the comprehensive evaluation reports for insights
- Examine the error analysis for model improvement ideas

## File Structure After Training

```
outputs/
├── best_model/              # Best model checkpoint
├── final_model/             # Final model after training
├── checkpoint-*/            # Intermediate checkpoints
├── config.json              # Training configuration
└── training_config.json     # Additional training metadata

evaluation_results/
├── comprehensive_report.json    # Complete evaluation results
├── *_predictions.json          # Detailed predictions
├── *_results.json             # Per-dataset results
└── summary.json               # Summary across datasets
```

Happy training! 🚀
