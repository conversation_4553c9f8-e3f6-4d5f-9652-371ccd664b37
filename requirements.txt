# Core ML libraries
torch>=2.0.0
transformers>=4.35.0
datasets>=2.14.0
tokenizers>=0.14.0

# ModernBERT specific
accelerate>=0.24.0
safetensors>=0.4.0

# Data processing
pandas>=1.5.0
numpy>=1.24.0
scikit-learn>=1.3.0
seaborn>=0.12.0
matplotlib>=3.7.0

# Evaluation and metrics
evaluate>=0.4.0
seqeval>=1.2.2

# Utilities
tqdm>=4.65.0
wandb>=0.15.0
tensorboard>=2.14.0
omegaconf>=2.3.0
hydra-core>=1.3.0

# Development
jupyter>=1.0.0
ipykernel>=6.25.0
black>=23.0.0
flake8>=6.0.0

# Optional: for advanced features
optuna>=3.4.0  # hyperparameter optimization
ray[tune]>=2.7.0  # distributed training
