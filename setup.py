from setuptools import setup, find_packages

setup(
    name="modernbert-hallucination-detection",
    version="0.1.0",
    description="ModernBERT-based hallucination detection for RAG systems",
    author="Your Name",
    author_email="<EMAIL>",
    packages=find_packages(),
    python_requires=">=3.8",
    install_requires=[
        "torch>=2.0.0",
        "transformers>=4.35.0",
        "datasets>=2.14.0",
        "tokenizers>=0.14.0",
        "accelerate>=0.24.0",
        "safetensors>=0.4.0",
        "pandas>=1.5.0",
        "numpy>=1.24.0",
        "scikit-learn>=1.3.0",
        "seaborn>=0.12.0",
        "matplotlib>=3.7.0",
        "evaluate>=0.4.0",
        "seqeval>=1.2.2",
        "tqdm>=4.65.0",
        "wandb>=0.15.0",
        "tensorboard>=2.14.0",
        "omegaconf>=2.3.0",
        "hydra-core>=1.3.0",
    ],
    extras_require={
        "dev": [
            "jupyter>=1.0.0",
            "ipykernel>=6.25.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
        ],
        "optimization": [
            "optuna>=3.4.0",
            "ray[tune]>=2.7.0",
        ],
    },
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
)
